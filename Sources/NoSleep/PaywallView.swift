import SwiftUI
import StoreKit

struct PaywallView: View {
    @EnvironmentObject var purchaseManager: PurchaseManager
    @Environment(\.dismiss) private var dismiss
    @State private var isAnimating = false
    @State private var selectedProduct: Product?
    @State private var isLoadingProducts = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var isPurchased = false
    @EnvironmentObject var languageManager: LanguageManager
    
    let products: [(id: String, name: String, price: String, period: String, popular: Bool)] = [
        ("com.nosleep.fullversion", "Lifetime", "¥19.99", "one time", false)
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with animation
            VStack(spacing: 16) {
                Image(systemName: "crown.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.yellow)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAnimating)
                
                VStack(spacing: 8) {
                    LocalizedText("Unlock NoSleep Pro")
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text("Unlock unlimited duration and premium features")
                        .font(.title3)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.top, 40)
            .padding(.bottom, 30)
            
            // Features
            VStack(spacing: 20) {
                FeatureRow(icon: "infinity", title: "Unlimited Usage".localized(with: languageManager), description: "No time limits".localized(with: languageManager))
                FeatureRow(icon: "shield.fill", title: "Ad-Free".localized(with: languageManager), description: "Clean experience".localized(with: languageManager))
                FeatureRow(icon: "sparkles", title: "Premium Features".localized(with: languageManager), description: "All future updates".localized(with: languageManager))
                FeatureRow(icon: "heart.fill", title: "Support Development".localized(with: languageManager), description: "Help us improve".localized(with: languageManager))
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 30)
            
            // Pricing options
            VStack(spacing: 12) {
                ForEach(products, id: \.id) { product in
                    ProductOptionView(
                        name: product.name,
                        price: product.price,
                        period: product.period,
                        popular: product.popular,
                        action: {
                            purchaseProduct(product.id)
                        }
                    )
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 20)
            
            // Restore button
            Button("Restore Purchase".localized(with: languageManager)) {
                Task {
                    await purchaseManager.restorePurchases()
                    if purchaseManager.isPurchased {
                        dismiss()
                    }
                }
            }
            .font(.caption)
            .foregroundColor(.secondary)
            .padding(.bottom, 20)
            
            // Terms
            HStack(spacing: 4) {
                Text("By purchasing, you agree to our".localized(with: languageManager))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Button("Terms".localized(with: languageManager)) {
                    // Open terms
                }
                .font(.caption2)
                .foregroundColor(.blue)
                
                Text("and".localized(with: languageManager))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Button("Privacy Policy".localized(with: languageManager)) {
                    // Open privacy policy
                }
                .font(.caption2)
                .foregroundColor(.blue)
            }
            .padding(.bottom, 20)
        }
        .frame(width: 480, height: 700)
        .overlay {
            if isPurchased {
                // 成功动画覆盖层
                VStack(spacing: 20) {
                    ZStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 100, height: 100)
                            .scaleEffect(isPurchased ? 1.2 : 0.0)
                            .opacity(isPurchased ? 0.8 : 0.0)
                            .animation(.easeInOut(duration: 0.5), value: isPurchased)
                        
                        Image(systemName: "checkmark")
                            .font(.system(size: 50, weight: .bold))
                            .foregroundColor(.white)
                            .scaleEffect(isPurchased ? 1.0 : 0.0)
                            .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0), value: isPurchased)
                    }
                    
                    Text("Purchase Successful!".localized(with: languageManager))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .opacity(isPurchased ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.5).delay(0.3), value: isPurchased)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(NSColor.windowBackgroundColor).opacity(0.95))
            }
        }
        .background(Color(NSColor.windowBackgroundColor))
        .onAppear {
            isAnimating = true
            loadProducts()
        }
        .onChange(of: purchaseManager.isPurchased) { _, newValue in
            if newValue {
                isPurchased = true
                // 延迟关闭以显示成功动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    dismiss()
                }
            }
        }
        .alert("Error".localized(with: languageManager), isPresented: $showError) {
            Button("OK".localized(with: languageManager), role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private func loadProducts() {
        isLoadingProducts = true
        Task {
            do {
                let productIDs = products.map { $0.id }
                _ = try await Product.products(for: productIDs)
                // Store products for later use
            } catch {
                errorMessage = "Failed to load products"
                showError = true
            }
            isLoadingProducts = false
        }
    }
    
    private func purchaseProduct(_ productID: String) {
        Task {
            isLoadingProducts = true
            await purchaseManager.purchase()
            isLoadingProducts = false
            
            if purchaseManager.isPurchased {
                dismiss()
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    @State private var isChecked = false
    
    var body: some View {
        HStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.green)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .font(.title2)
                .foregroundColor(.green)
                .opacity(isChecked ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.3), value: isChecked)
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation {
                    isChecked = true
                }
            }
        }
    }
}

struct ProductOptionView: View {
    let name: String
    let price: String
    let period: String
    let popular: Bool
    let action: () -> Void
    @EnvironmentObject var languageManager: LanguageManager
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(name)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if popular {
                            Text("MOST POPULAR".localized(with: languageManager))
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.orange)
                                .cornerRadius(8)
                        }
                    }
                    
                    Text("\(price) / \(period)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue.opacity(popular ? 0.1 : 0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue, lineWidth: popular ? 2 : 1)
                    )
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(popular ? 1.02 : 1.0)
        .shadow(color: popular ? Color.blue.opacity(0.3) : .clear, radius: popular ? 5 : 0, x: 0, y: popular ? 2 : 0)
    }
}

#Preview {
    PaywallView()
        .environmentObject(PurchaseManager())
}