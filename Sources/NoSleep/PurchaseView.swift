import SwiftUI
import AppKit

struct PurchaseView: View {
    @EnvironmentObject var purchaseManager: PurchaseManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    @State private var showPaywall = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 页面标题
                HStack(spacing: 12) {
                    Image(systemName: "crown.fill")
                        .font(.title2)
                        .foregroundColor(.yellow)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        LocalizedText("Purchase")
                            .font(.title)
                            .fontWeight(.bold)
                        LocalizedText("Unlock NoSleep Pro with premium features")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                
                // 当前状态
                VStack(spacing: 16) {
                    if purchaseManager.isPurchased {
                        // 已购买状态
                        VStack(spacing: 20) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.green)
                            
                            VStack(spacing: 8) {
                                Text("Full version purchased".localized(with: languageManager))
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primary)
                                
                                Text("Thank you for your support!".localized(with: languageManager))
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 30)
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(16)
                        
                    } else {
                        // 免费版本状态
                        VStack(spacing: 16) {
                            HStack {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.title2)
                                
                                VStack(spacing: 4) {
                                    Text("Free Version".localized(with: languageManager))
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    
                                    Text("Max 1 hour duration".localized(with: languageManager))
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.yellow.opacity(0.1))
                            .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                // 功能对比
                VStack(alignment: .leading, spacing: 16) {
                    Text("Feature Comparison".localized(with: languageManager))
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(spacing: 0) {
                        // 表头
                        HStack(spacing: 16) {
                            Text("")
                                .font(.caption)
                                .fontWeight(.bold)
                                .frame(width: 120, alignment: .leading)
                            
                            Spacer()
                            
                            HStack {
                                Text("Free Version".localized(with: languageManager))
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.yellow)
                            }
                            .frame(width: 80)
                            
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .frame(width: 1)
                            
                            HStack {
                                Text("Pro Version".localized(with: languageManager))
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                            .frame(width: 80)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color(NSColor.controlBackgroundColor))
                        .cornerRadius(8)
                        
                        VStack(spacing: 1) {
                            FeatureComparisonRow(
                                title: "Sleep Prevention".localized(with: languageManager),
                                trial: "Limited".localized(with: languageManager),
                                pro: "Unlimited".localized(with: languageManager)
                            )
                            
                            Divider()
                            
                            FeatureComparisonRow(
                                title: "Usage Time".localized(with: languageManager),
                                trial: "Max 1 hour duration".localized(with: languageManager),
                                pro: "No limits".localized(with: languageManager)
                            )
                            
                            Divider()
                            
                            FeatureComparisonRow(
                                title: "Premium Features".localized(with: languageManager),
                                trial: "Basic".localized(with: languageManager),
                                pro: "All included".localized(with: languageManager)
                            )
                            
                            Divider()
                            
                            FeatureComparisonRow(
                                title: "Future Updates".localized(with: languageManager),
                                trial: "Limited".localized(with: languageManager),
                                pro: "Free forever".localized(with: languageManager)
                            )
                        }
                    }
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
                }
                .padding(.horizontal, 20)
                
                // 购买按钮
                if !purchaseManager.isPurchased {
                    VStack(spacing: 16) {
                        Button(action: {
                            showPaywall = true
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "crown.fill")
                                    .font(.title2)
                                    .foregroundColor(.yellow)
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Unlock NoSleep Pro".localized(with: languageManager))
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                    
                                    Text("One-time purchase - ¥19.99".localized(with: languageManager))
                                        .font(.subheadline)
                                        .foregroundColor(.white.opacity(0.9))
                                }
                                
                                Spacer()
                                
                                Image(systemName: "arrow.right.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 20)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .foregroundColor(.white)
                            .cornerRadius(16)
                        }
                        .buttonStyle(.plain)
                        .shadow(color: Color.blue.opacity(0.3), radius: 8, x: 0, y: 4)
                        
                        HStack(spacing: 16) {
                            Button("Restore purchase".localized(with: languageManager)) {
                                Task {
                                    await purchaseManager.restorePurchases()
                                }
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .buttonStyle(.plain)
                        }
                    }
                    .padding(.horizontal, 20)
                }
                
                Spacer()
            }
        }
        .sheet(isPresented: $showPaywall) {
            PaywallView()
                .environmentObject(purchaseManager)
                .environmentObject(languageManager)
                .frame(width: 480, height: 700)
        }
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
}

struct FeatureComparisonRow: View {
    let title: String
    let trial: String
    let pro: String
    @EnvironmentObject var languageManager: LanguageManager
    
    var body: some View {
        HStack(spacing: 16) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .frame(width: 120, alignment: .leading)
            
            Spacer()
            
            HStack {
                Text(trial)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(width: 80)
            
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 1)
            
            HStack {
                Text(pro)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
            }
            .frame(width: 80)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
    }
}

#Preview {
    PurchaseView()
        .environmentObject(PurchaseManager())
        .environmentObject(LanguageManager())
}