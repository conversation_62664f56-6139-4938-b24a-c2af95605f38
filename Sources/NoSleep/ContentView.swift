import SwiftUI
import AppKit

struct ContentView: View {
    @EnvironmentObject var sleepManager: SleepManager
    @EnvironmentObject var purchaseManager: PurchaseManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var selectedTab = 0
    @State private var showPaywall = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部标题栏
            HStack {
                Image(systemName: "moon.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                LocalizedText("NoSleep")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 8)
            
            // 主要内容区域
            TabView(selection: $selectedTab) {
                MainView(selectedTab: $selectedTab)
                    .environmentObject(languageManager)
                    .tabItem {
                        Image(systemName: "power")
                        LocalizedText("Control")
                    }
                    .tag(0)
                
                SettingsView()
                    .environmentObject(languageManager)
                    .tabItem {
                        Image(systemName: "gear")
                        LocalizedText("Settings")
                    }
                    .tag(1)
                
                StatsView()
                    .environmentObject(languageManager)
                    .tabItem {
                        Image(systemName: "chart.bar")
                        LocalizedText("Stats")
                    }
                    .tag(2)
                
                PurchaseView()
                    .environmentObject(purchaseManager)
                    .environmentObject(languageManager)
                    .tabItem {
                        Image(systemName: "crown.fill")
                        LocalizedText("Purchase")
                    }
                    .tag(3)
            }
            .frame(width: 400, height: 520)
            // 监听语言变化并强制刷新整个TabView
            .onChange(of: languageManager.currentLanguage) { _, _ in
                // 强制刷新所有标签页
                selectedTab = selectedTab
            }
            .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
                // 强制刷新所有标签页
                selectedTab = selectedTab
            }
            
            Divider()
                .padding(.horizontal, 16)
            
            // 底部退出按钮
            HStack {
                Spacer()
                
                Button(action: {
                    NSApplication.shared.terminate(nil)
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: "power")
                            .font(.caption)
                        LocalizedText("Quit app")
                            .font(.caption)
                    }
                    .foregroundColor(.red)
                }
                .buttonStyle(.plain)
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
        .frame(width: 400, height: 600)
        .background(Color(NSColor.controlBackgroundColor))
        .onAppear {
            sleepManager.checkPowerState()
            sleepManager.updateDurationForPurchaseStatus(isPurchased: purchaseManager.isPurchased)
        }
        .onChange(of: purchaseManager.isPurchased) { _, _ in
            sleepManager.updateDurationForPurchaseStatus(isPurchased: purchaseManager.isPurchased)
        }
    }
}

struct MainView: View {
    @EnvironmentObject var sleepManager: SleepManager
    @EnvironmentObject var purchaseManager: PurchaseManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    @Binding var selectedTab: Int
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                appIntroductionView
                
                statusCardView
                
                controlButtonsView
                
                purchaseStatusCardView
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
    
    // App 功能介绍
    private var appIntroductionView: some View {
        VStack(spacing: 12) {
            Image(systemName: "moon.fill")
                .font(.system(size: 48))
                .foregroundColor(.blue)
            
            LocalizedText("NoSleep")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            LocalizedText("Prevent your Mac from sleeping")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }
    
    // 状态卡片
    private var statusCardView: some View {
        VStack(spacing: 16) {
            statusIndicatorView
            
            if sleepManager.isActive {
                remainingTimeView
            }
            
            PowerStatusView(powerSource: sleepManager.powerSource, batteryLevel: sleepManager.batteryLevel)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
        }
    }
    
    // 状态指示器
    private var statusIndicatorView: some View {
        HStack {
            Circle()
                .fill(sleepManager.isActive ? Color.green : Color.gray)
                .frame(width: 12, height: 12)
            LocalizedText(sleepManager.isActive ? "Sleep prevention activated" : "Sleep prevention not activated")
                .font(.headline)
                .fontWeight(.medium)
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
    
    // 剩余时间
    private var remainingTimeView: some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.secondary)
            LocalizedText("Remaining time")
                .font(.subheadline)
            Text(": \(sleepManager.formattedRemainingTime)")
                .font(.subheadline)
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
    
    // 主要控制按钮
    private var controlButtonsView: some View {
        VStack(spacing: 12) {
            mainControlButton
            
            debugButton
            
            freeVersionView
        }
    }
    
    // 主控制按钮
    private var mainControlButton: some View {
        Button(action: {
            sleepManager.toggleSleepPrevention()
            sleepManager.debugAssertionStatus()
        }) {
            HStack {
                Image(systemName: sleepManager.isActive ? "pause.fill" : "play.fill")
                    .font(.headline)
                Text(sleepManager.isActive ? "Stop sleep prevention".localized(with: languageManager) : "Start sleep prevention".localized(with: languageManager))
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(sleepManager.isActive ? Color.red : Color.blue)
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .buttonStyle(.plain)
    }
    
    // 调试按钮
    private var debugButton: some View {
        Button(action: {
            sleepManager.debugAssertionStatus()
        }) {
            HStack {
                Image(systemName: "ladybug")
                    .font(.caption)
                Text("Debug".localized(with: languageManager))
                    .font(.caption)
            }
            .foregroundColor(.gray)
        }
        .buttonStyle(.plain)
        .padding(.top, 4)
    }
    
    // 免费版本提示
    @ViewBuilder
    private var freeVersionView: some View {
        if !purchaseManager.isPurchased {
            HStack {
                Image(systemName: "star.fill")
                    .font(.caption)
                    .foregroundColor(.yellow)
                Text("Free Version".localized(with: languageManager))
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.yellow.opacity(0.1))
            .cornerRadius(6)
        }
    }
    
    
    // 购买状态卡片
    private var purchaseStatusCardView: some View {
        VStack(spacing: 16) {
            if purchaseManager.isPurchased {
                purchasedStatusView
            } else {
                freeVersionCardView
            }
        }
        .padding(.horizontal, 20)
    }
    
    // 已购买状态
    private var purchasedStatusView: some View {
        HStack(spacing: 16) {
            purchasedIconView
            
            purchasedContentView
            
            Spacer()
            
            purchasedStatusIconView
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.green.opacity(0.15),
                    Color.green.opacity(0.05)
                ]),
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.green.opacity(0.3), lineWidth: 1)
        )
    }
    
    // 已购买图标
    private var purchasedIconView: some View {
        ZStack {
            Circle()
                .fill(Color.green.opacity(0.2))
                .frame(width: 50, height: 50)
            
            Image(systemName: "crown.fill")
                .font(.title2)
                .foregroundColor(.yellow)
        }
    }
    
    // 已购买内容
    private var purchasedContentView: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("NoSleep Pro".localized(with: languageManager))
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text("Full version activated - Unlimited duration".localized(with: languageManager))
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    // 已购买状态图标
    private var purchasedStatusIconView: some View {
        Image(systemName: "checkmark.circle.fill")
            .font(.title)
            .foregroundColor(.green)
    }
    
    // 免费版本卡片
    private var freeVersionCardView: some View {
        VStack(spacing: 12) {
            freeVersionInfoBarView
            
            Divider()
                .padding(.horizontal, 8)
            
            upgradeButtonView
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    // 免费版本信息栏
    private var freeVersionInfoBarView: some View {
        HStack(spacing: 12) {
            freeVersionIconView
            
            freeVersionContentView
            
            Spacer()
        }
    }
    
    // 免费版本图标
    private var freeVersionIconView: some View {
        ZStack {
            Circle()
                .fill(Color.yellow.opacity(0.2))
                .frame(width: 40, height: 40)
            
            Image(systemName: "star.fill")
                .font(.headline)
                .foregroundColor(.yellow)
        }
    }
    
    // 免费版本内容
    private var freeVersionContentView: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text("Free Version".localized(with: languageManager))
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(purchaseManager.isPurchased ? "Unlimited duration".localized(with: languageManager) : "Max 1 hour duration".localized(with: languageManager))
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
        }
    }
    
    // 升级按钮
    private var upgradeButtonView: some View {
        Button(action: {
            selectedTab = 3
        }) {
            HStack(spacing: 8) {
                Image(systemName: "crown.fill")
                    .font(.headline)
                    .foregroundColor(.yellow)
                
                Text("Upgrade to Pro".localized(with: languageManager))
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("¥19.99")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white.opacity(0.9))
                
                Image(systemName: "arrow.right.circle.fill")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue,
                        Color.blue.opacity(0.8)
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
            .shadow(color: Color.blue.opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
    }
}

struct PowerStatusView: View {
    let powerSource: String
    let batteryLevel: Float
    @EnvironmentObject var languageManager: LanguageManager
    
    var body: some View {
        HStack {
            Image(systemName: powerSource == "AC Power" ? "powerplug.fill" : "battery.100")
                .foregroundColor(powerSource == "AC Power" ? .green : .blue)
                .font(.system(size: 14))
            
            VStack(alignment: .leading, spacing: 2) {
                Text(powerSource == "AC Power" ? "Power adapter".localized(with: languageManager) : "Battery power".localized(with: languageManager))
                    .font(.caption)
                    .fontWeight(.medium)
                
                if powerSource == "Battery Power" {
                    Text("Battery level: \(Int(batteryLevel * 100))%".localized(with: languageManager))
                        .font(.caption2)
                        .foregroundColor(batteryLevel > 0.2 ? .green : .red)
                }
            }
            
            Spacer()
            
            if powerSource == "Battery Power" {
                // 电池图标
                ZStack {
                    RoundedRectangle(cornerRadius: 2)
                        .stroke(Color.gray, lineWidth: 1)
                        .frame(width: 20, height: 10)
                    
                    RoundedRectangle(cornerRadius: 2)
                        .fill(batteryLevel > 0.2 ? Color.green : Color.red)
                        .frame(width: CGFloat(batteryLevel * 18), height: 8)
                    
                    Rectangle()
                        .fill(Color.gray)
                        .frame(width: 2, height: 4)
                        .offset(x: 11)
                }
            }
        }
    }
}