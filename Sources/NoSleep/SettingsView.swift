import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var sleepManager: SleepManager
    @EnvironmentObject var purchaseManager: PurchaseManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    @State private var isLanguageExpanded = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 页面标题
                VStack(alignment: .leading, spacing: 4) {
                    LocalizedText("Settings")
                        .font(.title)
                        .fontWeight(.bold)
                    LocalizedText("Customize your sleep prevention preferences")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                
                                
                // 电源条件设置
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "powerplug")
                            .foregroundColor(.green)
                            .font(.headline)
                        Text("Power conditions".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("These conditions determine when sleep prevention can start and will automatically stop it if no longer met.".localized(with: languageManager))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(6)
                        
                        VStack(spacing: 12) {
                            Toggle("Prevent sleep when on battery".localized(with: languageManager), isOn: $sleepManager.preventOnBattery)
                                .onChange(of: sleepManager.preventOnBattery) { _, _ in
                                    sleepManager.saveSettings()
                                }
                                .toggleStyle(CheckboxToggleStyle())
                            
                            Toggle("Prevent sleep when plugged in".localized(with: languageManager), isOn: $sleepManager.preventWhenPluggedIn)
                                .onChange(of: sleepManager.preventWhenPluggedIn) { _, _ in
                                    sleepManager.saveSettings()
                                }
                                .toggleStyle(CheckboxToggleStyle())
                            
                            if sleepManager.preventOnBattery {
                                VStack(alignment: .leading, spacing: 8) {
                                    HStack {
                                        Text("Minimum battery level".localized(with: languageManager))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Spacer()
                                        Text("\(Int(sleepManager.minimumBatteryLevel * 100))%")
                                            .font(.subheadline)
                                            .foregroundColor(.blue)
                                            .fontWeight(.semibold)
                                    }
                                    
                                    Slider(value: $sleepManager.minimumBatteryLevel, in: 0.05...0.5, step: 0.05)
                                        .onChange(of: sleepManager.minimumBatteryLevel) { _, _ in
                                            sleepManager.saveSettings()
                                        }
                                    
                                    Text("Sleep prevention will stop if battery drops below this level".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.top, 4)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(Color(NSColor.controlBackgroundColor))
                                .cornerRadius(8)
                            }
                        }
                        
                }
                }
                .padding(.horizontal, 20)
                
                // 语言设置
                LanguageSettingsView(isExpanded: $isLanguageExpanded)
                    .environmentObject(languageManager)
                    .padding(.bottom, 20)
                
                Spacer()
            }
        }
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
}

// 自定义切换样式
struct CheckboxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        Button(action: {
            configuration.isOn.toggle()
        }) {
            HStack {
                Image(systemName: configuration.isOn ? "checkmark.square.fill" : "square")
                    .foregroundColor(configuration.isOn ? .blue : .gray)
                    .font(.system(size: 16))
                configuration.label
                    .font(.subheadline)
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
        }
        .buttonStyle(.plain)
    }
}