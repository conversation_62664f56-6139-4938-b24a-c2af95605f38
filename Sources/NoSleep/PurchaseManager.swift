import Foundation
import StoreKit

@MainActor
class PurchaseManager: ObservableObject {
    @Published var isPurchased = false
    @Published var showPurchaseSheet = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let productID = "com.nosleep.fullversion"
    private var updateListenerTask: Task<Void, Error>?
    
    init() {
        loadPurchaseStatus()
        updateListenerTask = listenForTransactions()
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    private func loadPurchaseStatus() {
        // 从UserDefaults加载购买状态
        isPurchased = UserDefaults.standard.bool(forKey: "isPurchased")
    }
    
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)
                    await self.updatePurchasedProducts()
                    await transaction.finish()
                } catch {
                    print("Transaction failed: \(error)")
                }
            }
        }
    }
    
    private func checkVerified<T>(_ result: VerificationResult<T>) async throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    func purchase() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let products = try await Product.products(for: [productID])
            guard let product = products.first else {
                errorMessage = "No products available".localized
                isLoading = false
                return
            }
            
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try await checkVerified(verification)
                await updatePurchasedProducts()
                await transaction.finish()
                
            case .userCancelled:
                errorMessage = "Purchase was cancelled".localized
                
            case .pending:
                errorMessage = "Purchase is pending".localized
                
            @unknown default:
                errorMessage = "Unknown purchase result".localized
            }
            
        } catch {
            errorMessage = ("Purchase failed: " + error.localizedDescription).localized
        }
        
        isLoading = false
    }
    
    func restorePurchases() async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await AppStore.sync()
            await updatePurchasedProducts()
        } catch {
            errorMessage = ("Failed to restore purchases: " + error.localizedDescription).localized
        }
        
        isLoading = false
    }
    
    private func updatePurchasedProducts() async {
        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try await checkVerified(result)
                if transaction.productID == productID {
                    await MainActor.run {
                        isPurchased = true
                        // 保存到UserDefaults
                        UserDefaults.standard.set(true, forKey: "isPurchased")
                    }
                }
            } catch {
                print("Failed to verify transaction: \(error)")
            }
        }
    }
}

enum StoreError: Error {
    case failedVerification
}