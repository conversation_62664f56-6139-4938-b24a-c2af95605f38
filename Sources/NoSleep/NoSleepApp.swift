import SwiftUI
import AppKit

@main
struct NoSleepApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var sleepManager = SleepManager()
    @StateObject private var purchaseManager = PurchaseManager()
    @StateObject private var languageManager = LanguageManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(sleepManager)
                .environmentObject(purchaseManager)
                .environmentObject(languageManager)
                .onAppear {
                    appDelegate.sleepManager = sleepManager
                    appDelegate.purchaseManager = purchaseManager
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .windowToolbarStyle(.unified)
        .defaultSize(width: 400, height: 600)
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var sleepManager: SleepManager?
    var purchaseManager: PurchaseManager?
    var paywallWindow: NSWindow?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        NSApp.setActivationPolicy(.regular)
        
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        
        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "moon.fill", accessibilityDescription: "NoSleep".localized)
            button.action = #selector(showWindow)
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }
    }
    
    @objc @MainActor func showWindow() {
        NSApp.setActivationPolicy(.regular)
        NSApp.activate(ignoringOtherApps: true)
        
        // 显示正常窗口
        for window in NSApplication.shared.windows {
            if window.title == "NoSleep" {
                window.makeKeyAndOrderFront(nil)
                return
            }
        }
    }
    
    private func showPaywall() {
        // 如果付费窗口已存在，直接显示
        if let existingWindow = paywallWindow {
            existingWindow.makeKeyAndOrderFront(nil)
            return
        }
        
        // 创建新的付费窗口
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 480, height: 700),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "NoSleep Pro"
        window.center()
        window.isReleasedWhenClosed = false
        
        // 设置付费视图
        let paywallView = PaywallView()
            .environmentObject(sleepManager!)
            .environmentObject(purchaseManager!)
        
        window.contentView = NSHostingView(rootView: paywallView)
        
        // 存储窗口引用
        paywallWindow = window
        
        // 显示窗口
        window.makeKeyAndOrderFront(nil)
        
        // 监听窗口关闭
        NotificationCenter.default.addObserver(
            forName: NSWindow.willCloseNotification,
            object: window,
            queue: .main
        ) { [weak self] _ in
            self?.paywallWindow = nil
        }
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return false
    }
}