import SwiftUI

struct StatsView: View {
    @EnvironmentObject var sleepManager: SleepManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 页面标题
                VStack(alignment: .leading, spacing: 4) {
                    LocalizedText("Stats")
                        .font(.title)
                        .fontWeight(.bold)
                    LocalizedText("View your usage statistics")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                
                // 统计卡片
                VStack(spacing: 16) {
                    // 主要统计
                    VStack(spacing: 12) {
                        HStack {
                            Image(systemName: "clock.fill")
                                .foregroundColor(.blue)
                                .font(.headline)
                            Text("Usage statistics".localized(with: languageManager))
                                .font(.headline)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        
                        VStack(spacing: 16) {
                            // 总防休眠时间
                            HStack {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("Total sleep prevention time".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(sleepManager.formattedTotalTime)
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.blue)
                                }
                                Spacer()
                                
                                // 时间图标
                                ZStack {
                                    Circle()
                                        .fill(Color.blue.opacity(0.1))
                                        .frame(width: 60, height: 60)
                                    Image(systemName: "timer")
                                        .font(.title2)
                                        .foregroundColor(.blue)
                                }
                            }
                            
                            Divider()
                            
                            // 其他统计
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Current status".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(sleepManager.isActive ? "Activated".localized(with: languageManager) : "Not activated".localized(with: languageManager))
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                }
                                Spacer()
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Current mode".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(sleepManager.selectedDuration.localizedName)
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                }
                                Spacer()
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Power source".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(sleepManager.powerSource == "AC Power" ? "Power adapter".localized(with: languageManager) : "Battery".localized(with: languageManager))
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                }
                                Spacer()
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(12)
                }
                .padding(.horizontal, 20)
                
                // 使用习惯
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "gearshape.fill")
                            .foregroundColor(.green)
                            .font(.headline)
                        Text("Usage habits".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    
                    VStack(spacing: 8) {
                        StatRow(title: "Allow on battery".localized(with: languageManager), value: sleepManager.preventOnBattery ? "Yes".localized(with: languageManager) : "No".localized(with: languageManager))
                        StatRow(title: "Allow when plugged in".localized(with: languageManager), value: sleepManager.preventWhenPluggedIn ? "Yes".localized(with: languageManager) : "No".localized(with: languageManager))
                        StatRow(title: "Minimum battery requirement".localized(with: languageManager), value: "\(Int(sleepManager.minimumBatteryLevel * 100))%")
                        
                        if sleepManager.powerSource == "Battery Power" {
                            StatRow(title: "Battery level".localized(with: languageManager), value: "\(Int(sleepManager.batteryLevel * 100))%")
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                
                // 今日统计
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(.orange)
                            .font(.headline)
                        Text("Today's statistics".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    
                    VStack(spacing: 8) {
                        StatRow(title: "Today's usage".localized(with: languageManager), value: "2h 15m")
                        StatRow(title: "Launch count".localized(with: languageManager), value: "8")
                        StatRow(title: "Average duration".localized(with: languageManager), value: "17m")
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                
                // 使用提示
                VStack(spacing: 12) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(.yellow)
                            .font(.headline)
                        Text("Usage tips".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text("Long-term use of sleep prevention increases battery consumption".localized(with: languageManager))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text("Recommended to use when connected to power to protect battery life".localized(with: languageManager))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        HStack(alignment: .top, spacing: 8) {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                                .font(.caption)
                            Text("You can set battery level threshold to automatically stop sleep prevention".localized(with: languageManager))
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color.yellow.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
                
                Spacer()
            }
        }
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
}

struct StatRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .fontWeight(.medium)
        }
    }
}