import Foundation
import AppKit
import IOKit.pwr_mgt

class SleepManager: ObservableObject {
    @Published var isActive = false
    @Published var powerSource = "AC Power"
    @Published var batteryLevel: Float = 1.0
    @Published var remainingTime: TimeInterval = 0
    @Published var totalPreventedTime: TimeInterval = 0
    
    private var timer: Timer?
    private var startTime: Date?
    private var sleepAssertionID: UInt32 = 0
    private var displaySleepAssertionID: UInt32 = 0
    private var idleSleepAssertionID: UInt32 = 0
    
    enum Duration: String, CaseIterable {
        case oneHour = "1 hour"
        case unlimited = "unlimited"
        
        var timeInterval: TimeInterval {
            switch self {
            case .oneHour: return 60 * 60
            case .unlimited: return Double.greatestFiniteMagnitude
            }
        }
        
        var localizedName: String {
            switch self {
            case .oneHour: return "1 hour".localized
            case .unlimited: return "Unlimited".localized
            }
        }
    }
    
    @Published var selectedDuration: Duration = .oneHour
    @Published var preventOnBattery = true
    @Published var preventWhenPluggedIn = true
    @Published var minimumBatteryLevel: Float = 0.2
    
    init() {
        checkPowerState()
        startPowerMonitoring()
        loadSettings()
    }
    
    func updateDurationForPurchaseStatus(isPurchased: Bool) {
        selectedDuration = isPurchased ? .unlimited : .oneHour
        saveSettings()
    }
    
    func toggleSleepPrevention() {
        if isActive {
            stopSleepPrevention()
        } else {
            startSleepPrevention()
        }
    }
    
    private func startSleepPrevention() {
        guard canPreventSleep() else {
            print("Cannot prevent sleep due to conditions")
            return
        }
        
        // 创建多个断言来全面防止睡眠
        let systemAssertionID = preventSystemSleep()
        let displayAssertionID = preventDisplaySleep()
        let idleAssertionID = preventIdleSleep()
        
        if systemAssertionID != 0 && displayAssertionID != 0 && idleAssertionID != 0 {
            sleepAssertionID = systemAssertionID
            displaySleepAssertionID = displayAssertionID
            idleSleepAssertionID = idleAssertionID
            isActive = true
            startTime = Date()
            
            remainingTime = selectedDuration.timeInterval
            startTimer()
            
            saveStats()
            print("Sleep prevention started - System: \(systemAssertionID), Display: \(displayAssertionID), Idle: \(idleAssertionID)")
        } else {
            // 如果任何一个断言失败，清理所有断言
            if systemAssertionID != 0 {
                releaseSystemSleep(systemAssertionID)
            }
            if displayAssertionID != 0 {
                releaseDisplaySleep(displayAssertionID)
            }
            if idleAssertionID != 0 {
                releaseIdleSleep(idleAssertionID)
            }
            print("Failed to create one or more sleep assertions")
        }
    }
    
    private func stopSleepPrevention() {
        var releasedAssertions: [String] = []
        
        if sleepAssertionID != 0 {
            releaseSystemSleep(sleepAssertionID)
            releasedAssertions.append("System(\(sleepAssertionID))")
            sleepAssertionID = 0
        }
        
        if displaySleepAssertionID != 0 {
            releaseDisplaySleep(displaySleepAssertionID)
            releasedAssertions.append("Display(\(displaySleepAssertionID))")
            displaySleepAssertionID = 0
        }
        
        if idleSleepAssertionID != 0 {
            releaseIdleSleep(idleSleepAssertionID)
            releasedAssertions.append("Idle(\(idleSleepAssertionID))")
            idleSleepAssertionID = 0
        }
        
        isActive = false
        timer?.invalidate()
        timer = nil
        
        if let startTime = startTime {
            let interval = Date().timeIntervalSince(startTime)
            totalPreventedTime += interval
            saveStats()
        }
        
        print("Sleep prevention stopped - Released: \(releasedAssertions.joined(separator: ", "))")
    }
    
    private func preventSystemSleep() -> UInt32 {
        // 防止系统睡眠
        let reason = ("NoSleep: " + "Prevent system sleep".localized) as CFString
        var assertionID: UInt32 = 0
        
        let result = IOPMAssertionCreateWithName(
            kIOPMAssertionTypePreventSystemSleep as CFString,
            IOPMAssertionLevel(kIOPMAssertionLevelOn),
            reason,
            &assertionID
        )
        
        if result == kIOReturnSuccess {
            return assertionID
        } else {
            print("Failed to create system sleep assertion: \(result)")
            return 0
        }
    }
    
    private func preventDisplaySleep() -> UInt32 {
        // 防止显示器睡眠 - 这是最重要的，防止屏幕关闭
        let reason = ("NoSleep: " + "Prevent display sleep".localized) as CFString
        var assertionID: UInt32 = 0
        
        let result = IOPMAssertionCreateWithName(
            kIOPMAssertionTypePreventUserIdleDisplaySleep as CFString,
            IOPMAssertionLevel(kIOPMAssertionLevelOn),
            reason,
            &assertionID
        )
        
        if result == kIOReturnSuccess {
            return assertionID
        } else {
            print("Failed to create display sleep assertion: \(result)")
            return 0
        }
    }
    
    private func preventIdleSleep() -> UInt32 {
        // 防止空闲睡眠
        let reason = ("NoSleep: " + "Prevent idle sleep".localized) as CFString
        var assertionID: UInt32 = 0
        
        let result = IOPMAssertionCreateWithName(
            kIOPMAssertionTypePreventUserIdleSystemSleep as CFString,
            IOPMAssertionLevel(kIOPMAssertionLevelOn),
            reason,
            &assertionID
        )
        
        if result == kIOReturnSuccess {
            return assertionID
        } else {
            print("Failed to create idle sleep assertion: \(result)")
            return 0
        }
    }
    
    private func releaseSystemSleep(_ assertionID: UInt32) {
        let result = IOPMAssertionRelease(assertionID)
        if result == kIOReturnSuccess {
            print("Released system sleep assertion: \(assertionID)")
        } else {
            print("Failed to release system sleep assertion: \(assertionID), error: \(result)")
        }
    }
    
    private func releaseDisplaySleep(_ assertionID: UInt32) {
        let result = IOPMAssertionRelease(assertionID)
        if result == kIOReturnSuccess {
            print("Released display sleep assertion: \(assertionID)")
        } else {
            print("Failed to release display sleep assertion: \(assertionID), error: \(result)")
        }
    }
    
    private func releaseIdleSleep(_ assertionID: UInt32) {
        let result = IOPMAssertionRelease(assertionID)
        if result == kIOReturnSuccess {
            print("Released idle sleep assertion: \(assertionID)")
        } else {
            print("Failed to release idle sleep assertion: \(assertionID), error: \(result)")
        }
    }
    
    private func canPreventSleep() -> Bool {
        if powerSource == "AC Power" {
            return preventWhenPluggedIn
        } else {
            return preventOnBattery && batteryLevel >= minimumBatteryLevel
        }
    }
    
    func debugAssertionStatus() {
        print("=== NoSleep Debug Status ===")
        print("Active: \(isActive)")
        print("System Assertion ID: \(sleepAssertionID)")
        print("Display Assertion ID: \(displaySleepAssertionID)")
        print("Idle Assertion ID: \(idleSleepAssertionID)")
        print("Power Source: \(powerSource)")
        print("Battery Level: \(batteryLevel)")
        print("Can Prevent Sleep: \(canPreventSleep())")
        print("==========================")
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
    }
    
    private func updateTimer() {
        guard remainingTime > 0 && remainingTime != Double.greatestFiniteMagnitude else {
            if remainingTime == Double.greatestFiniteMagnitude {
                // Unlimited duration - only stop if power conditions are not met
                if !canPreventSleep() {
                    stopSleepPrevention()
                }
                return
            } else {
                stopSleepPrevention()
                return
            }
        }
        
        remainingTime -= 1
        
        if !canPreventSleep() {
            stopSleepPrevention()
        }
    }
    
    func checkPowerState() {
        // 使用 pmset 命令检查电源状态
        let process = Process()
        process.launchPath = "/usr/bin/pmset"
        process.arguments = ["-g", "ps"]
        
        let outputPipe = Pipe()
        process.standardOutput = outputPipe
        
        do {
            try process.run()
            let data = outputPipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: data, encoding: .utf8) ?? ""
            
            if output.contains("AC Power") {
                self.powerSource = "AC Power"
            } else {
                self.powerSource = "Battery Power"
                // 简单解析电池电量
                if let range = output.range(of: "(\\d+)%", options: .regularExpression) {
                    let percentageStr = output[range].replacingOccurrences(of: "%", with: "")
                    if let percentage = Int(percentageStr) {
                        self.batteryLevel = Float(percentage) / 100.0
                    }
                }
            }
        } catch {
            print("Failed to check power state: \(error)")
        }
    }
    
    private func startPowerMonitoring() {
        // 监听电源状态变化
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name.NSProcessInfoPowerStateDidChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.checkPowerState()
            
            if self?.isActive == true && !(self?.canPreventSleep() ?? false) {
                self?.stopSleepPrevention()
            }
        }
        
        // 保持定时器作为备用
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.checkPowerState()
            
            if self?.isActive == true && !(self?.canPreventSleep() ?? false) {
                self?.stopSleepPrevention()
            }
        }
    }
    
    private func loadSettings() {
        if let duration = UserDefaults.standard.string(forKey: "selectedDuration"),
           let savedDuration = Duration(rawValue: duration) {
            selectedDuration = savedDuration
        } else {
            // Reset to default if saved duration is no longer valid
            selectedDuration = .oneHour
        }
        
        // 使用 object(forKey:) 来检查键是否存在，避免默认值问题
        preventOnBattery = UserDefaults.standard.object(forKey: "preventOnBattery") as? Bool ?? true
        preventWhenPluggedIn = UserDefaults.standard.object(forKey: "preventWhenPluggedIn") as? Bool ?? true
        minimumBatteryLevel = UserDefaults.standard.object(forKey: "minimumBatteryLevel") as? Float ?? 0.2
        totalPreventedTime = UserDefaults.standard.double(forKey: "totalPreventedTime")
    }
    
    func saveSettings() {
        UserDefaults.standard.set(selectedDuration.rawValue, forKey: "selectedDuration")
        UserDefaults.standard.set(preventOnBattery, forKey: "preventOnBattery")
        UserDefaults.standard.set(preventWhenPluggedIn, forKey: "preventWhenPluggedIn")
        UserDefaults.standard.set(minimumBatteryLevel, forKey: "minimumBatteryLevel")
    }
    
    private func saveStats() {
        UserDefaults.standard.set(totalPreventedTime, forKey: "totalPreventedTime")
    }
    
    var formattedRemainingTime: String {
        if remainingTime == Double.greatestFiniteMagnitude {
            return "Unlimited".localized
        }
        
        let hours = Int(remainingTime) / 3600
        let minutes = (Int(remainingTime) % 3600) / 60
        let seconds = Int(remainingTime) % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
    
    var formattedTotalTime: String {
        let hours = Int(totalPreventedTime) / 3600
        let minutes = (Int(totalPreventedTime) % 3600) / 60
        
        if hours > 0 {
            return String(format: "%d小时%d分钟", hours, minutes)
        } else {
            return String(format: "%d分钟", minutes)
        }
    }
}