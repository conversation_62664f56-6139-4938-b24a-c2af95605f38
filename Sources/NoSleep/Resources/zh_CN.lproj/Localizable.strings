/* App Name */
"NoSleep" = "NoSleep";

/* Main Tab */
"Control" = "控制";
"Settings" = "设置";
"Stats" = "统计";

/* Main View */
"Prevent your Mac from sleeping" = "防止您的 Mac 进入休眠状态";
"Sleep prevention activated" = "防休眠已激活";
"Sleep prevention not activated" = "防休眠未激活";
"Remaining time" = "剩余时间：";
"Start sleep prevention" = "开始防休眠";
"Stop sleep prevention" = "停止防休眠";
"Limited time offer" = "限时优惠";
"Unlock full version" = "解锁完整版";
"One-time purchase" = "一次性买断";
"One-time purchase - ¥19.99" = "一次性买断 - ¥19.99";
"Restore purchase" = "恢复购买";

/* Power Status */
"Power adapter" = "电源适配器";
"Battery power" = "电池供电";
"Battery level" = "电量";

/* Settings */
"Customize your sleep prevention preferences" = "自定义您的防休眠偏好";
"Power conditions" = "电源条件";
"Prevent sleep when on battery" = "使用电池时防止休眠";
"Prevent sleep when plugged in" = "连接电源时防止休眠";
"Minimum battery level" = "最低电池电量";
"Sleep prevention will stop if battery drops below this level" = "当电池电量低于此水平时，防休眠功能将自动停止";
"These conditions determine when sleep prevention can start and will automatically stop it if no longer met." = "这些条件决定了防休眠功能何时可以启动，并在条件不再满足时自动停止。";
"Note" = "注意";
"With a duration limit set, sleep prevention will stop either when time runs out OR when power conditions are no longer met, whichever comes first." = "设置了持续时间限制后，防休眠功能将在时间耗尽或电源条件不再满足时停止（以先到者为准）。";
"Purchase status" = "购买状态";
"Full version purchased" = "已购买完整版";
"Full version activated" = "完整版已激活";
"Thank you for your support" = "感谢您的支持";

/* Stats */
"View your usage statistics" = "查看您的使用情况";
"Usage statistics" = "使用统计";
"Total sleep prevention time" = "总防休眠时间";
"Current status" = "当前状态";
"Current mode" = "当前模式";
"Power source" = "电源来源";
"Usage habits" = "使用习惯";
"Allow on battery" = "电池时允许";
"Allow when plugged in" = "电源时允许";
"Minimum battery requirement" = "最低电量要求";
"Today's statistics" = "今日统计";
"Today's usage" = "今日使用";
"Launch count" = "启动次数";
"Average duration" = "平均时长";
"Usage tips" = "使用提示";
"Long-term use of sleep prevention increases battery consumption" = "长时间使用防休眠功能会增加电池消耗";
"Recommended to use when connected to power to protect battery life" = "建议在连接电源时使用，以保护电池寿命";
"You can set battery level threshold to automatically stop sleep prevention" = "可以设置电池电量阈值，自动停止防休眠";

/* Duration Options */
"1 hour" = "1小时";
"Unlimited" = "无限";
"Unlimited duration" = "无限时长";

/* Status Menu */
"Status" = "状态";
"Open control panel" = "打开控制面板";
"Quit app" = "退出应用";

/* Common */
"Yes" = "是";
"No" = "否";
"Activated" = "激活";
"Not activated" = "未激活";
"System default" = "系统默认";
"Language" = "语言";
"current" = "当前";

/* Error Messages */
"No products available" = "没有可用的产品";
"Purchase was cancelled" = "购买已取消";
"Purchase is pending" = "购买待处理";
"Unknown purchase result" = "未知的购买结果";
"Purchase failed: " = "购买失败: ";
"Failed to restore purchases: " = "恢复购买失败: ";

/* System Messages */
"Prevent system sleep" = "防止系统睡眠";
"Prevent display sleep" = "防止显示器睡眠";
"Prevent idle sleep" = "防止空闲睡眠";

/* Loading Messages */
"Loading..." = "加载中...";
"Debug" = "调试";

/* Paywall */
"Unlock NoSleep Pro" = "解锁 NoSleep Pro";
"Unlimited Usage" = "无限使用";
"No time limits" = "无时间限制";
"Ad-Free" = "无广告";
"Clean experience" = "纯净体验";
"Premium Features" = "高级功能";
"All future updates" = "所有未来更新";
"Support Development" = "支持开发";
"Help us improve" = "帮助我们改进";
"Monthly" = "月度";
"Yearly" = "年度";
"Lifetime" = "终身";
"month" = "月";
"year" = "年";
"one time" = "一次性";
"MOST POPULAR" = "最受欢迎";
"Restore Purchase" = "恢复购买";
"Terms" = "服务条款";
"Privacy Policy" = "隐私政策";
"By purchasing, you agree to our" = "购买即表示您同意我们的";
"and" = "和";
"Purchase Successful!" = "购买成功！";

/* Purchase Tab */
"Purchase" = "购买";
"Unlock NoSleep Pro with premium features" = "解锁 NoSleep Pro 高级功能";
"Feature Comparison" = "功能对比";
"Limited" = "有限";
"No limits" = "无限制";
"Basic" = "基础";
"All included" = "全部包含";
"Free forever" = "永久免费";
"Unlock NoSleep Pro" = "解锁 NoSleep Pro";
"Free Version" = "免费版";
"Pro Version" = "专业版";
"Sleep Prevention" = "防休眠功能";
"Usage Time" = "使用时间";
"Premium Features" = "高级功能";
"Future Updates" = "未来更新";
"Upgrade to Pro" = "升级到专业版";
"Max 1 hour duration" = "最长1小时时长";