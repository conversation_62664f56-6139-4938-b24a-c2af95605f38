# NoSleep App Sandbox 权限配置指南

## 概述

本文档详细说明了 NoSleep 应用在 App Store Connect 中需要配置的 App Sandbox 权限信息。

## 权限配置清单

### ✅ 需要启用的权限

| 权限类型 | 权限密钥 | 状态 | 用途 |
|---------|---------|------|------|
| App Sandbox | `com.apple.security.app-sandbox` | 启用 | Mac App Store 必需 |
| Network - Outgoing Connections | `com.apple.security.network.client` | 启用 | StoreKit 购买验证 |
| Apple Events | `com.apple.security.automation.apple-events` | 启用 | 系统电源管理交互 |

### ❌ 不需要的权限

- User Selected File (Read Only)
- User Selected File (Read/Write)
- Downloads Folder (Read Only/Write)
- Pictures/Music/Movies Folder
- USB/Printing/Camera/Microphone
- Contacts/Location/Calendar/Address Book

## App Store Connect 配置详情

### 1. App Sandbox
**状态**: ✅ 启用  
**使用信息**:
```
Required for Mac App Store distribution. The app runs in a sandboxed environment to ensure security and system integrity.
```

**中文版本**:
```
Mac App Store 分发所必需。应用在沙盒环境中运行以确保安全性和系统完整性。
```

### 2. Network - Outgoing Connections (Client)
**权限密钥**: `com.apple.security.network.client`  
**状态**: ✅ 启用  
**使用信息**:
```
Required for in-app purchase functionality through StoreKit. The app needs to communicate with Apple's servers to validate purchases and manage subscription status.
```

**中文版本**:
```
通过 StoreKit 实现应用内购买功能所必需。应用需要与 Apple 服务器通信以验证购买并管理订阅状态。
```

### 3. Apple Events
**权限密钥**: `com.apple.security.automation.apple-events`  
**状态**: ✅ 启用  
**使用信息**:
```
Required to interact with system power management APIs (IOKit) to prevent the Mac from sleeping. This is the core functionality of the app - preventing system sleep when activated by the user.
```

**中文版本**:
```
与系统电源管理 API (IOKit) 交互以防止 Mac 休眠所必需。这是应用的核心功能 - 在用户激活时防止系统休眠。
```

## 配置步骤

### 在 App Store Connect 中操作

1. **登录 App Store Connect**
   - 访问 https://appstoreconnect.apple.com
   - 使用你的 Apple Developer 账户登录

2. **选择应用**
   - 在 "My Apps" 中找到 NoSleep 应用
   - 点击进入应用详情页面

3. **进入 App Information**
   - 在左侧导航栏中选择 "App Information"
   - 滚动到 "App Sandbox Information" 部分

4. **配置权限**
   - **App Sandbox**: 选择 "Yes"
   - **Network**: 
     - Outgoing Connections (Client): 选择 "Yes"
     - Incoming Connections (Server): 选择 "No"
   - **File Access**: 全部选择 "No"
   - **Hardware**: 全部选择 "No"
   - **App Data**: 全部选择 "No"

5. **填写使用信息**
   - 在每个启用权限旁边的文本框中填入上述对应的使用信息
   - 建议使用英文版本

6. **保存配置**
   - 点击页面顶部的 "Save" 按钮保存配置

### 在 Xcode 项目中验证

确保 entitlements 文件与 App Store Connect 配置一致：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.automation.apple-events</key>
    <true/>
</dict>
</plist>
```

## 重要注意事项

### 最小权限原则
- 只申请应用真正需要的权限
- 避免申请不必要的权限，这可能导致审核延迟或被拒

### 审核建议
- 使用信息要简洁明确
- 避免使用过于技术性的术语
- 确保权限使用与应用实际功能匹配

### 常见问题
1. **为什么不需要文件访问权限？**
   - NoSleep 应用不读取或写入用户文件
   - 所有设置都存储在 UserDefaults 中

2. **Apple Events 权限的作用？**
   - 用于调用 IOKit 的 IOPMAssertion API
   - 这是防休眠功能的核心实现

3. **网络权限的必要性？**
   - StoreKit 需要与 Apple 服务器通信
   - 用于验证应用内购买

## 更新记录

- **2024-01-XX**: 初始版本
- **2024-01-XX**: 移除不必要的文件访问权限

---

**注意**: 本配置基于 NoSleep v1.0 的功能需求。如果应用功能发生变化，请相应更新权限配置。
