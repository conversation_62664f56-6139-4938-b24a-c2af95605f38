#!/usr/bin/env swift

import Foundation
import Security

// 简化的Keychain管理器用于测试
class TestKeychainManager {
    static let shared = TestKeychainManager()
    
    private let obfuscationKey = "NoSleep2024"
    
    private init() {}
    
    private func obfuscate(_ data: Data) -> Data {
        var result = data
        let keyBytes = Array(obfuscationKey.utf8)
        
        for i in 0..<result.count {
            result[i] = result[i] ^ keyBytes[i % keyBytes.count]
        }
        
        return result
    }
    
    private func deobfuscate(_ data: Data) -> Data {
        return obfuscate(data)
    }
    
    func save(_ data: Data, forKey key: String) -> Bool {
        let obfuscatedData = obfuscate(data)
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: obfuscatedData,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        SecItemDelete(query as CFDictionary)
        return SecItemAdd(query as CFDictionary, nil) == errSecSuccess
    }
    
    func load(forKey key: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess, let data = result as? Data {
            return deobfuscate(data)
        }
        
        return nil
    }
    
    func exists(forKey key: String) -> Bool {
        return load(forKey: key) != nil
    }
    
    func delete(forKey key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        return SecItemDelete(query as CFDictionary) == errSecSuccess
    }
}

// 测试函数
func testKeychainPersistence() {
    let manager = TestKeychainManager.shared
    let testKey = "com.nosleep.test"
    let testData = "Test Data at \(Date())".data(using: .utf8)!
    
    print("🧪 Testing Keychain persistence...")
    print(String(repeating: "=", count: 50))
    
    // 1. 保存测试数据
    print("1. Saving test data to Keychain...")
    let saveSuccess = manager.save(testData, forKey: testKey)
    print("   Save result: \(saveSuccess ? "✅ Success" : "❌ Failed")")
    
    // 2. 立即读取验证
    print("\n2. Reading data immediately...")
    if let loadedData = manager.load(forKey: testKey),
       let loadedString = String(data: loadedData, encoding: .utf8) {
        print("   ✅ Data loaded successfully: \(loadedString)")
    } else {
        print("   ❌ Failed to load data")
    }
    
    // 3. 检查Keychain中是否存在
    print("\n3. Checking if data exists in Keychain...")
    print("   Exists: \(manager.exists(forKey: testKey) ? "✅ Yes" : "❌ No")")
    
    // 4. 显示Keychain中的所有NoSleep相关项
    print("\n4. Listing all NoSleep-related Keychain items:")
    let keys = [
        "com.nosleep.ispurchased",
        "com.nosleep.firstlaunch",
        "com.nosleep.test"
    ]
    
    for key in keys {
        if manager.exists(forKey: key) {
            print("   ✅ Found: \(key)")
        }
    }
    
    print("\n" + String(repeating: "=", count: 50))
    print("📝 Instructions:")
    print("1. Run this script to save test data")
    print("2. Delete the NoSleep app")
    print("3. Run this script again")
    print("4. If data still exists, Keychain persistence is working!")
    print("\n💡 Note: Keychain data persists even after app deletion")
    print("   This prevents users from resetting their trial by reinstalling")
}

// 运行测试
testKeychainPersistence()