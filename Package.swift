// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "NoSleep",
    defaultLocalization: "en",
    platforms: [
        .macOS(.v14)
    ],
    products: [
        .executable(
            name: "<PERSON>Sleep",
            targets: ["NoSleep"]
        )
    ],
    targets: [
        .executableTarget(
            name: "NoSleep",
            dependencies: [],
            resources: [
                .process("Assets.xcassets"),
                .process("AppStoreConnect_Notification_Service_Extension.entitlements"),
                .process("Resources"),
                .process("Preview Content"),
                .process("NoSleep.entitlements")
            ],
            linkerSettings: [
                .linkedFramework("IOKit"),
                .linkedFramework("StoreKit")
            ]
        )
    ]
)