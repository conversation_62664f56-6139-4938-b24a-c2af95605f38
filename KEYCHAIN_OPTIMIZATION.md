# 钥匙串访问优化说明

## 优化内容

### 1. 减少钥匙串访问频率
- 添加了缓存机制，使用 `UserDefaults` 缓存购买状态
- 首次启动时优先使用 `UserDefaults` 数据，避免立即访问钥匙串
- 后台静默迁移旧数据到钥匙串

### 2. 优化用户体验
- 添加了加载指示器，让用户知道正在访问钥匙串
- 异步处理钥匙串操作，避免阻塞UI
- 只在必要时才提示用户输入钥匙串密码

### 3. 缓存策略
- `cachedPurchaseStatus`: 缓存购买状态，减少钥匙串读取
- 保留 `UserDefaults` 作为快速读取的备用方案
- 新数据仍保存到钥匙串以确保安全性

## 用户提示

**为什么还会提示钥匙串密码？**
1. **首次授权**: 应用首次访问钥匙串时需要用户授权
2. **应用更新**: 如果应用代码签名发生变化，macOS 会要求重新授权
3. **安全机制**: 这是 macOS 保护敏感数据的标准安全机制

**优化后的体验：**
- 应用启动更快（使用缓存数据）
- 只在必要时才访问钥匙串
- 加载时显示进度指示器
- 后台静默处理数据迁移

## 技术细节

### 钥匙串 vs UserDefaults
- **钥匙串**: 加密存储，需要授权，适合敏感数据
- **UserDefaults**: 明文存储，快速访问，适合缓存

### 数据流
1. 应用启动 → 检查 UserDefaults 缓存
2. 如果有缓存 → 立即显示，后台验证
3. 如果没有缓存 → 访问钥匙串（可能需要密码）
4. 保存数据 → 同时写入钥匙串和缓存