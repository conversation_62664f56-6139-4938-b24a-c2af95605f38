# NoSleep - Mac 防休眠应用

一个基于 SwiftUI 的 macOS 菜单栏应用，用于防止 Mac 进入休眠状态。

## 主要功能

### 🌙 防止休眠
- **多种持续时间选项**：无限制、30分钟、1小时、6小时、1天、1周
- **智能条件判断**：根据电源状态和电池电量自动调整
- **实时状态监控**：显示剩余时间和电源状态

### ⚡ 电源管理
- **电池模式**：可设置最低电池电量要求
- **电源适配器模式**：连接电源时的独立设置
- **自动检测**：实时监控电源状态变化

### 💰 付费模式
- **免费试用**：1天完整功能试用
- **一次性买断**：¥19.99 永久使用
- **购买恢复**：支持重新下载后恢复购买

### 📊 使用统计
- **总使用时间**：累计防休眠时间统计
- **使用习惯**：电源偏好和设置记录
- **今日统计**：当日使用情况分析

### 🎨 精美界面
- **现代设计**：采用最新 SwiftUI 设计语言
- **菜单栏集成**：点击菜单栏图标快速访问
- **多标签界面**：控制、设置、统计分页显示

## 技术栈

- **SwiftUI** - 现代化 UI 框架
- **IOKit** - 电源管理和休眠控制
- **StoreKit** - 应用内购买
- **macOS 14.0+** - 最低系统要求

## 项目结构

```
NoSleep/
├── NoSleepApp.swift          # 应用入口和菜单栏设置
├── ContentView.swift         # 主界面和标签页
├── SleepManager.swift        # 防休眠核心功能
├── SettingsView.swift       # 设置页面
├── StatsView.swift          # 统计页面
├── PurchaseManager.swift    # 付费管理
└── Assets.xcassets/         # 应用资源
```

## 使用说明

1. **启动应用**：应用会在菜单栏显示月亮图标
2. **点击图标**：打开主界面
3. **开始防休眠**：点击"开始防休眠"按钮
4. **设置参数**：在设置页面配置持续时间和电源条件
5. **查看统计**：在统计页面查看使用情况

## 开发说明

### Xcode 项目配置
- 使用 Xcode 15.0+
- 目标系统：macOS 14.0+
- 开发语言：Swift 5.0

### StoreKit 配置
- 需要在 App Store Connect 配置应用内购买产品
- 产品 ID：`com.yourcompany.nosleep.full`

### 权限说明
- 应用沙盒：已启用
- 网络访问：用于 StoreKit 购买验证
- 文件访问：用户选择文件只读权限

## 安装方法

1. 克隆项目到本地
2. 用 Xcode 打开 `NoSleep.xcodeproj`
3. 选择目标设备（Mac）
4. 点击运行按钮

## 注意事项

- 应用需要辅助功能权限来防止系统休眠
- 首次使用时需要授权访问电源信息
- 试用期为首次启动后 24 小时

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。