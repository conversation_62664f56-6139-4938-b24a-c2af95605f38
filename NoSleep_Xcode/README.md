# NoSleep Xcode 项目设置指南

## 概述
这个文件夹包含了一个标准的 Xcode 项目，用于将 NoSleep 应用上架到 App Store。

## 使用步骤

### 1. 打开 Xcode 项目
在 Xcode 中打开 `NoSleep_Xcode/NoSleep.xcodeproj`

### 2. 配置开发者信息
1. 在 Xcode 中选择项目文件（蓝色图标）
2. 在 "Signing & Capabilities" 标签页中：
   - 选择您的 Team（开发者账户）
   - 确保 Bundle Identifier 为 `tech.jiangkang.NoSleep`
   - 确保 Automatically manage signing 已勾选

### 3. 配置应用信息
项目已包含：
- ✅ 正确的 Bundle ID (`tech.jiangkang.NoSleep`)
- ✅ 应用图标
- ✅ 多语言支持
- ✅ 权限配置文件 (entitlements)
- ✅ 所有本地化资源

### 4. 构建和测试
1. 在 Xcode 中选择目标设备为 "My Mac"
2. 按 Cmd+B 构建项目
3. 按 Cmd+R 运行应用

### 5. 准备上架
1. 在 Xcode 中选择 Product > Archive
2. 在 Archives 窗口中点击 "Upload to App Store"
3. 按照提示完成上传

### 6. 在 App Store Connect 中完成配置
1. 登录 [App Store Connect](https://appstoreconnect.apple.com)
2. 创建新应用：
   - 名称：NoSleep
   - Bundle ID：tech.jiangkang.NoSleep
   - 平台：Mac
   - SKU：NoSleep
3. 填写应用信息
4. 上传应用截图
5. 添加隐私政策 URL
6. 添加技术支持 URL
7. 提交审核

## 注意事项
- 确保您的 Apple Developer 账户已启用 Mac 应用分发功能
- 所有本地化文件已包含在项目中
- 项目配置了最低系统要求 macOS 14.0
- 已包含 IOKit 和 StoreKit 框架链接

## 技术支持
如遇到问题，请参考项目根目录的技术支持文档。