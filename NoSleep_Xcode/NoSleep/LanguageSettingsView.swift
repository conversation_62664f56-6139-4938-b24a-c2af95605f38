import SwiftUI

struct LanguageSettingsView: View {
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    @Binding var isExpanded: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.purple)
                    .font(.headline)
                LocalizedText("Language")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                HStack {
                    Text(getCurrentLanguageDisplayName())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Image(systemName: isExpanded ? "chevron.down" : "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .contentShape(Rectangle())
            .onTapGesture {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            }
            
            if isExpanded {
                VStack(spacing: 8) {
                    // Auto option
                    HStack {
                        LocalizedText("System default")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                        if languageManager.currentLanguage == "auto" {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                                .font(.headline)
                        } else {
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                .frame(width: 20, height: 20)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        languageManager.currentLanguage == "auto" ? 
                        Color.blue.opacity(0.1) : 
                        Color(NSColor.controlBackgroundColor)
                    )
                    .cornerRadius(8)
                    .onTapGesture {
                        languageManager.setLanguage("auto")
                        refreshTrigger.toggle()
                    }
                    
                    Divider()
                    
                    // Language options
                    ForEach(Localization.supportedLanguages, id: \.self) { language in
                        HStack {
                            HStack {
                                Text(languageManager.localizedString(Localization.languageNames[language] ?? language))
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                if language == Locale.current.language.languageCode?.identifier && languageManager.currentLanguage == "auto" {
                                    Text(languageManager.localizedString("(current)"))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            Spacer()
                            if languageManager.currentLanguage == language {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                                    .font(.headline)
                            } else {
                                Circle()
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    .frame(width: 20, height: 20)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            languageManager.currentLanguage == language ? 
                            Color.blue.opacity(0.1) : 
                            Color(NSColor.controlBackgroundColor)
                        )
                        .cornerRadius(8)
                        .onTapGesture {
                            languageManager.setLanguage(language)
                            refreshTrigger.toggle()
                        }
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding(.horizontal, 20)
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
    
    private func getCurrentLanguageDisplayName() -> String {
        if languageManager.currentLanguage == "auto" {
            return languageManager.localizedString("System default")
        } else {
            return languageManager.localizedString(Localization.languageNames[languageManager.currentLanguage] ?? languageManager.currentLanguage)
        }
    }
}