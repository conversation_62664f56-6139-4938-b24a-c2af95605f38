/* App Name */
"NoSleep" = "NoSleep";

/* Main Tab */
"Control" = "Control";
"Settings" = "Settings";
"Stats" = "Stats";

/* Main View */
"Prevent your Mac from sleeping" = "Prevent your Mac from sleeping";
"Sleep prevention activated" = "Sleep prevention activated";
"Sleep prevention not activated" = "Sleep prevention not activated";
"Remaining time" = "Remaining time";
"Start sleep prevention" = "Start sleep prevention";
"Stop sleep prevention" = "Stop sleep prevention";
"Limited time offer" = "Limited time offer";
"Unlock full version" = "Unlock full version";
"One-time purchase" = "One-time purchase";
"One-time purchase - ¥19.99" = "One-time purchase - ¥19.99";
"Restore purchase" = "Restore purchase";

/* Power Status */
"Power adapter" = "Power adapter";
"Battery power" = "Battery power";
"Battery level" = "Battery level";

/* Settings */
"Customize your sleep prevention preferences" = "Customize your sleep prevention preferences";
"Duration" = "Duration";
"Power conditions" = "Power conditions";
"Prevent sleep when on battery" = "Prevent sleep when on battery";
"Prevent sleep when plugged in" = "Prevent sleep when plugged in";
"Minimum battery level" = "Minimum battery level";
"Sleep prevention will stop if battery drops below this level" = "Sleep prevention will stop if battery drops below this level";
"These conditions determine when sleep prevention can start and will automatically stop it if no longer met." = "These conditions determine when sleep prevention can start and will automatically stop it if no longer met.";
"Note" = "Note";
"With a duration limit set, sleep prevention will stop either when time runs out OR when power conditions are no longer met, whichever comes first." = "With a duration limit set, sleep prevention will stop either when time runs out OR when power conditions are no longer met, whichever comes first.";
"Purchase status" = "Purchase status";
"Full version purchased" = "Full version purchased";
"Full version activated" = "Full version activated";
"Thank you for your support" = "Thank you for your support!";

/* Stats */
"View your usage statistics" = "View your usage statistics";
"Usage statistics" = "Usage statistics";
"Total sleep prevention time" = "Total sleep prevention time";
"Current status" = "Current status";
"Current mode" = "Current mode";
"Power source" = "Power source";
"Usage habits" = "Usage habits";
"Allow on battery" = "Allow on battery";
"Allow when plugged in" = "Allow when plugged in";
"Minimum battery requirement" = "Minimum battery requirement";
"Today's statistics" = "Today's statistics";
"Today's usage" = "Today's usage";
"Launch count" = "Launch count";
"Average duration" = "Average duration";
"Usage tips" = "Usage tips";
"Long-term use of sleep prevention increases battery consumption" = "Long-term use of sleep prevention increases battery consumption";
"Recommended to use when connected to power to protect battery life" = "Recommended to use when connected to power to protect battery life";
"You can set battery level threshold to automatically stop sleep prevention" = "You can set battery level threshold to automatically stop sleep prevention";

/* Duration Options */
"Unlimited" = "Unlimited";
"30 minutes" = "30 minutes";
"1 hour" = "1 hour";
"6 hours" = "6 hours";
"1 day" = "1 day";
"1 week" = "1 week";
"Unlimited duration" = "Unlimited duration";

/* Status Menu */
"Status" = "Status";
"Open control panel" = "Open control panel";
"Quit app" = "Quit app";

/* Common */
"Yes" = "Yes";
"No" = "No";
"Activated" = "Activated";
"Not activated" = "Not activated";
"System default" = "System default";
"Language" = "Language";
"current" = "current";

/* Error Messages */

/* System Messages */
"Prevent system sleep" = "Prevent system sleep";
"Prevent display sleep" = "Prevent display sleep";
"Prevent idle sleep" = "Prevent idle sleep";

/* Loading Messages */
"Loading..." = "Loading...";
"Debug" = "Debug";

/* Error Messages */
"No products available" = "No products available";
"Purchase was cancelled" = "Purchase was cancelled";
"Purchase is pending" = "Purchase is pending";
"Unknown purchase result" = "Unknown purchase result";
"Purchase failed: " = "Purchase failed: ";
"Failed to restore purchases: " = "Failed to restore purchases: ";

/* Paywall */
"Unlock NoSleep Pro" = "Unlock NoSleep Pro";
"Unlimited Usage" = "Unlimited Usage";
"No time limits" = "No time limits";
"Ad-Free" = "Ad-Free";
"Clean experience" = "Clean experience";
"Premium Features" = "Premium Features";
"All future updates" = "All future updates";
"Support Development" = "Support Development";
"Help us improve" = "Help us improve";
"Monthly" = "Monthly";
"Yearly" = "Yearly";
"Lifetime" = "Lifetime";
"month" = "month";
"year" = "year";
"one time" = "one time";
"MOST POPULAR" = "MOST POPULAR";
"Terms" = "Terms";
"Privacy Policy" = "Privacy Policy";
"By purchasing, you agree to our" = "By purchasing, you agree to our";
"and" = "and";
"Purchase Successful!" = "Purchase Successful!";

/* Purchase Tab */
"Purchase" = "Purchase";
"Unlock NoSleep Pro with premium features" = "Unlock NoSleep Pro with premium features";
"Feature Comparison" = "Feature Comparison";
"Limited" = "Limited";
"No limits" = "No limits";
"Basic" = "Basic";
"All included" = "All included";
"Free forever" = "Free forever";
"Unlock NoSleep Pro" = "Unlock NoSleep Pro";
"Free Version" = "Free Version";
"Pro Version" = "Pro Version";
"Sleep Prevention" = "Sleep Prevention";
"Usage Time" = "Usage Time";
"Premium Features" = "Premium Features";
"Future Updates" = "Future Updates";
"Upgrade to Pro" = "Upgrade to Pro";
"Max 1 hour duration" = "Max 1 hour duration";
"NoSleep Pro" = "NoSleep Pro";
"Full version activated - Unlimited duration" = "Full version activated - Unlimited duration";

