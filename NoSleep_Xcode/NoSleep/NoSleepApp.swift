import SwiftUI
import AppKit

@main
struct NoSleepApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var sleepManager = SleepManager()
    @StateObject private var purchaseManager = PurchaseManager()
    @StateObject private var languageManager = LanguageManager()
    
    var body: some Scene {
        WindowGroup("NoSleep") {
            ContentView()
                .environmentObject(sleepManager)
                .environmentObject(purchaseManager)
                .environmentObject(languageManager)
                .onAppear {
                    appDelegate.sleepManager = sleepManager
                    // 将窗口引用传递给 AppDelegate
                    if let window = NSApplication.shared.windows.first {
                        appDelegate.window = window
                    }
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .windowToolbarStyle(.unified)
        .defaultSize(width: 400, height: 600)
        .commands {
            // 添加自定义命令以支持窗口重新打开
            CommandGroup(replacing: .newItem) {
                <PERSON><PERSON>("New Window") {
                    appDelegate.showWindow()
                }
                .keyboardShortcut("n", modifiers: [.command, .shift])
            }
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var sleepManager: SleepManager?
    var window: NSWindow?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        NSApp.setActivationPolicy(.regular)
        
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        
        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "moon.fill", accessibilityDescription: "NoSleep".localized)
            button.action = #selector(statusItemClicked)
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }
        
        // 添加一次性的窗口关闭通知监听
        NotificationCenter.default.addObserver(
            forName: NSWindow.willCloseNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.windowWillClose(notification)
        }
    }
    
    @objc @MainActor func statusItemClicked() {
        let event = NSApp.currentEvent
        
        if event?.type == .rightMouseUp {
            // 右键显示菜单
            showMenu()
        } else {
            // 左键显示窗口
            showWindow()
        }
    }
    
    @objc @MainActor func showWindow() {
        NSApp.setActivationPolicy(.regular)
        NSApp.activate(ignoringOtherApps: true)
        
        // 检查是否已有可见窗口
        if let existingWindow = window, existingWindow.isVisible {
            existingWindow.makeKeyAndOrderFront(nil)
            // 确保窗口在前台显示
            existingWindow.level = .floating
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                existingWindow.level = .normal
            }
            return
        }
        
        // 查找所有窗口
        let windows = NSApplication.shared.windows
        var noSleepWindow: NSWindow?
        
        for window in windows {
            if window.title == "NoSleep" {
                noSleepWindow = window
                break
            }
        }
        
        if let window = noSleepWindow {
            self.window = window
            // 如果窗口最小化了，先取消最小化
            if window.isMiniaturized {
                window.deminiaturize(nil)
            }
            // 确保窗口可见并置顶
            window.makeKeyAndOrderFront(nil)
            window.orderFrontRegardless()
            // 临时提高窗口级别确保显示
            window.level = .floating
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                window.level = .normal
            }
        } else {
            // 如果没有找到窗口，创建新窗口
            let newWindow = NSWindow(
                contentRect: NSRect(x: 100, y: 100, width: 400, height: 600),
                styleMask: [.titled, .closable, .miniaturizable, .resizable],
                backing: .buffered,
                defer: false
            )
            newWindow.title = "NoSleep"
            newWindow.center()
            
            // 创建 SwiftUI 视图控制器
            let contentView = ContentView()
                .environmentObject(sleepManager!)
                .environmentObject(PurchaseManager())
                .environmentObject(LanguageManager())
            
            let hostingController = NSHostingController(rootView: contentView)
            newWindow.contentViewController = hostingController
            
            self.window = newWindow
            // 确保窗口显示在前台
            newWindow.makeKeyAndOrderFront(nil)
            newWindow.orderFrontRegardless()
            // 临时提高窗口级别
            newWindow.level = .floating
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                newWindow.level = .normal
            }
        }
    }
    
    func showMenu() {
        let menu = NSMenu()
        
        let showItem = NSMenuItem(title: "Open control panel".localized, action: #selector(showWindow), keyEquivalent: "")
        showItem.target = self
        menu.addItem(showItem)
        
        // 添加当前状态
        menu.addItem(NSMenuItem.separator())
        let statusMenuItem = NSMenuItem(title: sleepManager?.isActive == true ? "Sleep prevention activated".localized : "Sleep prevention not activated".localized, action: nil, keyEquivalent: "")
        statusMenuItem.isEnabled = false
        menu.addItem(statusMenuItem)
        
        menu.addItem(NSMenuItem.separator())
        let quitItem = NSMenuItem(title: "Quit app".localized, action: #selector(quitApp), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)
        
        self.statusItem?.menu = menu
        self.statusItem?.button?.performClick(nil)
        self.statusItem?.menu = nil
    }
    
    @objc func quitApp() {
        NSApp.terminate(nil)
    }
    
    @objc func windowWillClose(_ notification: Notification) {
        if let closingWindow = notification.object as? NSWindow,
           closingWindow.title == "NoSleep" {
            // 延迟切换到后台模式，给用户时间重新打开窗口
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // 检查是否还有其他可见窗口
                let visibleWindows = NSApplication.shared.windows.filter { $0.isVisible && $0.title == "NoSleep" }
                if visibleWindows.isEmpty {
                    NSApp.setActivationPolicy(.accessory)
                }
            }
            
            // 清理窗口引用
            if self.window == closingWindow {
                self.window = nil
            }
        }
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return false
    }
}