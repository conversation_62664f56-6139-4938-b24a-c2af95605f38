import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var sleepManager: SleepManager
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    @State private var isDurationExpanded = false
    @State private var isLanguageExpanded = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 页面标题
                VStack(alignment: .leading, spacing: 4) {
                    LocalizedText("Settings")
                        .font(.title)
                        .fontWeight(.bold)
                    LocalizedText("Customize your sleep prevention preferences")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
                
                // 持续时间设置
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(.blue)
                            .font(.headline)
                        Text("Duration".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                        HStack {
                            Text(sleepManager.selectedDuration.localizedName)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Image(systemName: isDurationExpanded ? "chevron.down" : "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isDurationExpanded.toggle()
                        }
                    }
                    
                    if isDurationExpanded {
                        VStack(spacing: 8) {
                            ForEach(SleepManager.Duration.allCases, id: \.self) { duration in
                                HStack {
                                    Text(duration.localizedName)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    Spacer()
                                    if sleepManager.selectedDuration == duration {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.blue)
                                            .font(.headline)
                                    } else {
                                        Circle()
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                            .frame(width: 20, height: 20)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    sleepManager.selectedDuration == duration ? 
                                    Color.blue.opacity(0.1) : 
                                    Color(NSColor.controlBackgroundColor)
                                )
                                .cornerRadius(8)
                                .onTapGesture {
                                    sleepManager.selectedDuration = duration
                                    sleepManager.saveSettings()
                                    withAnimation(.easeInOut(duration: 0.2)) {
                                        isDurationExpanded = false
                                    }
                                }
                            }
                        }
                        .padding(.top, 8)
                    }
                }
                .padding(.horizontal, 20)
                
                // 电源条件设置
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "powerplug")
                            .foregroundColor(.green)
                            .font(.headline)
                        Text("Power conditions".localized(with: languageManager))
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("These conditions determine when sleep prevention can start and will automatically stop it if no longer met.".localized(with: languageManager))
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(6)
                        
                        VStack(spacing: 12) {
                            Toggle("Prevent sleep when on battery".localized(with: languageManager), isOn: $sleepManager.preventOnBattery)
                                .onChange(of: sleepManager.preventOnBattery) { _, _ in
                                    sleepManager.saveSettings()
                                }
                                .toggleStyle(CheckboxToggleStyle())
                            
                            Toggle("Prevent sleep when plugged in".localized(with: languageManager), isOn: $sleepManager.preventWhenPluggedIn)
                                .onChange(of: sleepManager.preventWhenPluggedIn) { _, _ in
                                    sleepManager.saveSettings()
                                }
                                .toggleStyle(CheckboxToggleStyle())
                            
                            if sleepManager.preventOnBattery {
                                VStack(alignment: .leading, spacing: 8) {
                                    HStack {
                                        Text("Minimum battery level".localized(with: languageManager))
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Spacer()
                                        Text("\(Int(sleepManager.minimumBatteryLevel * 100))%")
                                            .font(.subheadline)
                                            .foregroundColor(.blue)
                                            .fontWeight(.semibold)
                                    }
                                    
                                    Slider(value: $sleepManager.minimumBatteryLevel, in: 0.05...0.5, step: 0.05)
                                        .onChange(of: sleepManager.minimumBatteryLevel) { _, _ in
                                            sleepManager.saveSettings()
                                        }
                                    
                                    Text("Sleep prevention will stop if battery drops below this level".localized(with: languageManager))
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.top, 4)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(Color(NSColor.controlBackgroundColor))
                                .cornerRadius(8)
                            }
                        }
                        
                        if sleepManager.selectedDuration != .unlimited {
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Image(systemName: "info.circle")
                                        .foregroundColor(.blue)
                                    Text("Note".localized(with: languageManager))
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                }
                                Text("With a duration limit set, sleep prevention will stop either when time runs out OR when power conditions are no longer met, whichever comes first.".localized(with: languageManager))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.yellow.opacity(0.15))
                            .cornerRadius(6)
                            .padding(.top, 8)
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                // 语言设置
                LanguageSettingsView(isExpanded: $isLanguageExpanded)
                    .environmentObject(languageManager)
                    .padding(.bottom, 20)
                
                Spacer()
            }
        }
        // 监听语言变化并强制刷新
        .onReceive(languageManager.$currentLanguage) { _ in
            refreshTrigger.toggle()
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
            refreshTrigger.toggle()
        }
    }
}

// 自定义切换样式
struct CheckboxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        Button(action: {
            configuration.isOn.toggle()
        }) {
            HStack {
                Image(systemName: configuration.isOn ? "checkmark.square.fill" : "square")
                    .foregroundColor(configuration.isOn ? .blue : .gray)
                    .font(.system(size: 16))
                configuration.label
                    .font(.subheadline)
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
        }
        .buttonStyle(.plain)
    }
}