import Foundation
import SwiftUI
import Combine

extension Notification.Name {
    static let languageChanged = Notification.Name("languageChanged")
}

class LanguageManager: ObservableObject {
    @Published var currentLanguage: String = "auto"
    @Published var effectiveLanguage: String = "en"
    @AppStorage("AppLanguage") private var storedLanguage: String = "auto"
    @AppStorage("HasMigratedLanguageToAuto") private var hasMigratedLanguageToAuto: Bool = false
    
    init() {
        // One-time migration to reset language to auto (System Default)
        if !hasMigratedLanguageToAuto {
            storedLanguage = "auto"
            hasMigratedLanguageToAuto = true
        }
        
        currentLanguage = storedLanguage
        updateEffectiveLanguage()
        #if DEBUG
        print("LanguageManager initialized - current: \(currentLanguage), effective: \(effectiveLanguage)")
        #endif
    }
    
    // Reset to system default (auto)
    func resetToSystemDefault() {
        setLanguage("auto")
    }
    
    func setLanguage(_ language: String) {
        storedLanguage = language
        currentLanguage = language
        updateEffectiveLanguage()
        // 强制通知所有观察者
        objectWillChange.send()
        // 发送自定义通知以触发UI更新
        NotificationCenter.default.post(name: .languageChanged, object: nil)
        
        // 只在开发时打印语言变更信息
        #if DEBUG
        print("Language changed to: \(language), effective: \(effectiveLanguage)")
        #endif
    }
    
    private func updateEffectiveLanguage() {
        if currentLanguage == "auto" {
            let systemLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            // 处理语言映射：将系统语言映射到我们的本地化文件
            effectiveLanguage = mapSystemLanguageToSupported(systemLanguage)
            #if DEBUG
            print("System language: \(systemLanguage), mapped to: \(effectiveLanguage)")
            #endif
        } else {
            effectiveLanguage = currentLanguage
        }
    }
    
    private func mapSystemLanguageToSupported(_ systemLanguage: String) -> String {
        // 将系统语言映射到我们支持的语言
        // 注意：构建系统会将语言代码转换为小写
        switch systemLanguage {
        case "zh":
            return "zh_cn"  // 中文系统使用简体中文（构建后是小写）
        case "ja":
            return "ja"
        case "ko":
            return "ko"
        case "es":
            return "es"
        case "fr":
            return "fr"
        case "de":
            return "de"
        case "pt":
            return "pt"
        case "ru":
            return "ru"
        case "ar":
            return "ar"
        case "hi":
            return "hi"
        default:
            return "en"
        }
    }
    
    func localizedString(_ key: String, comment: String = "") -> String {
        let language = effectiveLanguage
        
        // 将语言代码转换为 bundle 中的实际目录名
        let bundleLanguage = Localization.convertToBundleLanguage(language)
        
        // 首先尝试从主 bundle 查找
        if let path = Bundle.main.path(forResource: bundleLanguage, ofType: "lproj"),
           let bundle = Bundle(path: path) {
            let result = NSLocalizedString(key, bundle: bundle, comment: comment)
            // 只在开发时打印关键信息
            #if DEBUG
            if key == "Control" || key == "Settings" || key == "Stats" {
                print("✅ Loading '\(key)' in \(bundleLanguage): '\(result)'")
            }
            #endif
            return result
        }
        
        // 如果在主 bundle 中找不到，尝试在其他位置查找
        #if DEBUG
        // 在开发模式下，尝试查找实际的 bundle 位置
        let possibleBundlePaths = [
            Bundle.main.bundlePath + "/NoSleep_NoSleep.bundle",
            Bundle.main.bundlePath + "/../../debug/NoSleep_NoSleep.bundle"
        ]
        
        for bundlePath in possibleBundlePaths {
            if let fullBundle = Bundle(path: bundlePath),
               let lprojPath = fullBundle.path(forResource: bundleLanguage, ofType: "lproj"),
               let lprojBundle = Bundle(path: lprojPath) {
                let result = NSLocalizedString(key, bundle: lprojBundle, comment: comment)
                print("✅ Found in alternate bundle - Loading '\(key)' in \(bundleLanguage): '\(result)'")
                return result
            }
        }
        #endif
        
        #if DEBUG
        print("❌ Could not find bundle for language \(bundleLanguage)")
        print("Bundle main path: \(Bundle.main.bundlePath)")
        print("Bundle resource path: \(Bundle.main.resourcePath ?? "nil")")
        print("Bundle paths available:")
        if let resourcesPath = Bundle.main.resourcePath {
            let contents = try? FileManager.default.contentsOfDirectory(atPath: resourcesPath)
            if let lprojDirs = contents?.filter({ $0.hasSuffix(".lproj") }) {
                print(lprojDirs)
                // 检查每个 lproj 目录的内容
                for dir in lprojDirs {
                    let dirPath = resourcesPath + "/" + dir
                    let dirContents = try? FileManager.default.contentsOfDirectory(atPath: dirPath)
                    print("  \(dir): \(dirContents ?? [])")
                }
            }
        }
        #endif
        
        // 如果找不到对应语言，回退到英语
        if let path = Bundle.main.path(forResource: "en", ofType: "lproj"),
           let bundle = Bundle(path: path) {
            return NSLocalizedString(key, bundle: bundle, comment: comment)
        }
        
        // 最后回退到 mainBundle
        return NSLocalizedString(key, comment: comment)
    }
    
    // 强制刷新UI的方法
    func forceRefresh() {
        objectWillChange.send()
    }
}


struct Localization {
    static var shared = LanguageManager()
    
    static func localizedString(_ key: String) -> String {
        return shared.localizedString(key)
    }
    
    static func localizedString(_ key: String, comment: String) -> String {
        return shared.localizedString(key, comment: comment)
    }
    
    static var currentLanguage: String {
        return shared.effectiveLanguage
    }
    
    static var supportedLanguages: [String] {
        return ["en", "zh_CN", "zh_TW", "ja", "ko", "es", "fr", "de", "pt", "ru", "ar", "hi"]
    }
    
    static var languageNames: [String: String] {
        return [
            "en": "English",
            "zh_CN": "简体中文",
            "zh_TW": "繁體中文",
            "ja": "日本語",
            "ko": "한국어",
            "es": "Español",
            "fr": "Français",
            "de": "Deutsch",
            "pt": "Português",
            "ru": "Русский",
            "ar": "العربية",
            "hi": "हिन्दी"
        ]
    }
    
    // 将用户选择的语言代码转换为构建后的实际目录名
    static func convertToBundleLanguage(_ language: String) -> String {
        switch language {
        case "zh_CN":
            return "zh_CN"
        case "zh_TW":
            return "zh_TW"
        default:
            return language.lowercased()
        }
    }
}

// 可观察的本地化包装器
class ObservableLocalization: ObservableObject {
    @Published var languageManager: LanguageManager
    
    init(languageManager: LanguageManager) {
        self.languageManager = languageManager
        // 监听语言变化
        languageManager.objectWillChange.sink { [weak self] in
            self?.objectWillChange.send()
        }.store(in: &cancellables)
    }
    
    private var cancellables: Set<AnyCancellable> = []
    
    func localizedString(_ key: String, comment: String = "") -> String {
        return languageManager.localizedString(key, comment: comment)
    }
}

extension String {
    var localized: String {
        return Localization.shared.localizedString(self)
    }
    
    func localized(comment: String) -> String {
        return Localization.shared.localizedString(self, comment: comment)
    }
    
    // 可观察的本地化版本，用于需要实时更新的场景
    func localized(with manager: LanguageManager) -> String {
        return manager.localizedString(self)
    }
}

// 创建一个可观察的本地化视图包装器
struct LocalizedText: View {
    let key: String
    let comment: String
    @EnvironmentObject var languageManager: LanguageManager
    @State private var refreshTrigger = false
    
    init(_ key: String, comment: String = "") {
        self.key = key
        self.comment = comment
    }
    
    var body: some View {
        Text(languageManager.localizedString(key, comment: comment))
            // 监听语言变化并强制刷新
            .onChange(of: languageManager.currentLanguage) { _, _ in
                refreshTrigger.toggle()
            }
            .onReceive(NotificationCenter.default.publisher(for: .languageChanged)) { _ in
                refreshTrigger.toggle()
            }
    }
}