// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		4C0C3A012B8F1F5B00123456 /* NoSleepApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A002B8F1F5B00123456 /* NoSleepApp.swift */; };
		4C0C3A032B8F1F5B00123456 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A022B8F1F5B00123456 /* ContentView.swift */; };
		4C0C3A052B8F1F5C00123456 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A042B8F1F5C00123456 /* Assets.xcassets */; };
		4C0C3A082B8F1F5C00123456 /* Preview Content/Preview Content/Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A072B8F1F5C00123456 /* Preview Content/Preview Content/Preview Assets.xcassets */; };
		4C0C3A162B8F1F5F00123456 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A152B8F1F5F00123456 /* SettingsView.swift */; };
		4C0C3A182B8F1F6000123456 /* SleepManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A172B8F1F6000123456 /* SleepManager.swift */; };
		4C0C3A1A2B8F1F6100123456 /* StatsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A192B8F1F6100123456 /* StatsView.swift */; };
		4C0C3A1C2B8F1F6200123456 /* LanguageSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A1B2B8F1F6200123456 /* LanguageSettingsView.swift */; };
		4C0C3A1E2B8F1F6300123456 /* Localization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A1D2B8F1F6300123456 /* Localization.swift */; };
		4C0C3A2F2B8F1F7100123456 /* en.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A112B8F1F5E00123456 /* en.lproj */; };
		4C0C3A302B8F1F7200123456 /* zh_CN.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A132B8F1F5E00123456 /* zh_CN.lproj */; };
		4C0C3A312B8F1F7300123456 /* ar.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A252B8F1F6700123456 /* ar.lproj */; };
		4C0C3A322B8F1F7400123456 /* de.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A262B8F1F6800123456 /* de.lproj */; };
		4C0C3A332B8F1F7500123456 /* es.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A272B8F1F6900123456 /* es.lproj */; };
		4C0C3A342B8F1F7600123456 /* fr.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A282B8F1F6A00123456 /* fr.lproj */; };
		4C0C3A352B8F1F7700123456 /* hi.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A292B8F1F6B00123456 /* hi.lproj */; };
		4C0C3A362B8F1F7800123456 /* ja.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A2A2B8F1F6C00123456 /* ja.lproj */; };
		4C0C3A372B8F1F7900123456 /* ko.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A2B2B8F1F6D00123456 /* ko.lproj */; };
		4C0C3A382B8F1F7A00123456 /* pt.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A2C2B8F1F6E00123456 /* pt.lproj */; };
		4C0C3A392B8F1F7B00123456 /* ru.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A2D2B8F1F6F00123456 /* ru.lproj */; };
		4C0C3A3A2B8F1F7C00123456 /* zh_TW.lproj in Resources */ = {isa = PBXBuildFile; fileRef = 4C0C3A2E2B8F1F7000123456 /* zh_TW.lproj */; };
		4C0C3A3B2B8F1F7D00123456 /* PurchaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A3A2B8F1F7D00123456 /* PurchaseManager.swift */; };
		4C0C3A3C2B8F1F7E00123456 /* PaywallView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A3B2B8F1F7E00123456 /* PaywallView.swift */; };
		4C0C3A3D2B8F1F7F00123456 /* PurchaseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0C3A3C2B8F1F7F00123456 /* PurchaseView.swift */; };
		94D12CBD2E50E98500B3DDA8 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 94D12CBC2E50E98500B3DDA8 /* StoreKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		4C0C39FD2B8F1F5B00123456 /* NoSleep.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NoSleep.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4C0C3A002B8F1F5B00123456 /* NoSleepApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoSleepApp.swift; sourceTree = "<group>"; };
		4C0C3A022B8F1F5B00123456 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		4C0C3A042B8F1F5C00123456 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		4C0C3A072B8F1F5C00123456 /* Preview Content/Preview Content/Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Content/Preview Content/Preview Assets.xcassets"; sourceTree = "<group>"; };
		4C0C3A0C2B8F1F5D00123456 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4C0C3A0E2B8F1F5D00123456 /* NoSleep.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = NoSleep.entitlements; sourceTree = "<group>"; };
		4C0C3A112B8F1F5E00123456 /* en.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = en.lproj; sourceTree = "<group>"; };
		4C0C3A132B8F1F5E00123456 /* zh_CN.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = zh_CN.lproj; sourceTree = "<group>"; };
		4C0C3A152B8F1F5F00123456 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		4C0C3A172B8F1F6000123456 /* SleepManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SleepManager.swift; sourceTree = "<group>"; };
		4C0C3A192B8F1F6100123456 /* StatsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatsView.swift; sourceTree = "<group>"; };
		4C0C3A1B2B8F1F6200123456 /* LanguageSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageSettingsView.swift; sourceTree = "<group>"; };
		4C0C3A1D2B8F1F6300123456 /* Localization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Localization.swift; sourceTree = "<group>"; };
		4C0C3A252B8F1F6700123456 /* ar.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = ar.lproj; sourceTree = "<group>"; };
		4C0C3A262B8F1F6800123456 /* de.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = de.lproj; sourceTree = "<group>"; };
		4C0C3A272B8F1F6900123456 /* es.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = es.lproj; sourceTree = "<group>"; };
		4C0C3A282B8F1F6A00123456 /* fr.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = fr.lproj; sourceTree = "<group>"; };
		4C0C3A292B8F1F6B00123456 /* hi.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = hi.lproj; sourceTree = "<group>"; };
		4C0C3A2A2B8F1F6C00123456 /* ja.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = ja.lproj; sourceTree = "<group>"; };
		4C0C3A2B2B8F1F6D00123456 /* ko.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = ko.lproj; sourceTree = "<group>"; };
		4C0C3A2C2B8F1F6E00123456 /* pt.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = pt.lproj; sourceTree = "<group>"; };
		4C0C3A2D2B8F1F6F00123456 /* ru.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = ru.lproj; sourceTree = "<group>"; };
		4C0C3A2E2B8F1F7000123456 /* zh_TW.lproj */ = {isa = PBXFileReference; lastKnownFileType = folder; path = zh_TW.lproj; sourceTree = "<group>"; };
		4C0C3A3A2B8F1F7D00123456 /* PurchaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseManager.swift; sourceTree = "<group>"; };
		4C0C3A3B2B8F1F7E00123456 /* PaywallView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaywallView.swift; sourceTree = "<group>"; };
		4C0C3A3C2B8F1F7F00123456 /* PurchaseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseView.swift; sourceTree = "<group>"; };
		94D12CBC2E50E98500B3DDA8 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4C0C39FA2B8F1F5B00123456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				94D12CBD2E50E98500B3DDA8 /* StoreKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4C0C39F42B8F1F5B00123456 = {
			isa = PBXGroup;
			children = (
				4C0C3A052B8F1F5B00123456 /* NoSleep */,
				94D12CBB2E50E98500B3DDA8 /* Frameworks */,
				4C0C39FF2B8F1F5B00123456 /* Products */,
			);
			sourceTree = "<group>";
		};
		4C0C39FF2B8F1F5B00123456 /* Products */ = {
			isa = PBXGroup;
			children = (
				4C0C39FD2B8F1F5B00123456 /* NoSleep.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4C0C3A052B8F1F5B00123456 /* NoSleep */ = {
			isa = PBXGroup;
			children = (
				4C0C3A002B8F1F5B00123456 /* NoSleepApp.swift */,
				4C0C3A022B8F1F5B00123456 /* ContentView.swift */,
				4C0C3A152B8F1F5F00123456 /* SettingsView.swift */,
				4C0C3A172B8F1F6000123456 /* SleepManager.swift */,
				4C0C3A192B8F1F6100123456 /* StatsView.swift */,
				4C0C3A1B2B8F1F6200123456 /* LanguageSettingsView.swift */,
				4C0C3A1D2B8F1F6300123456 /* Localization.swift */,
				4C0C3A3A2B8F1F7D00123456 /* PurchaseManager.swift */,
				4C0C3A3B2B8F1F7E00123456 /* PaywallView.swift */,
				4C0C3A3C2B8F1F7F00123456 /* PurchaseView.swift */,
				4C0C3A042B8F1F5C00123456 /* Assets.xcassets */,
				4C0C3A0E2B8F1F5D00123456 /* NoSleep.entitlements */,
				4C0C3A0C2B8F1F5D00123456 /* Info.plist */,
				4C0C3A102B8F1F5D00123456 /* Resources */,
				4C0C3A062B8F1F5C00123456 /* Preview Content */,
			);
			path = NoSleep;
			sourceTree = "<group>";
		};
		4C0C3A062B8F1F5C00123456 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				4C0C3A072B8F1F5C00123456 /* Preview Content/Preview Content/Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		4C0C3A102B8F1F5D00123456 /* Resources */ = {
			isa = PBXGroup;
			children = (
				4C0C3A112B8F1F5E00123456 /* en.lproj */,
				4C0C3A132B8F1F5E00123456 /* zh_CN.lproj */,
				4C0C3A252B8F1F6700123456 /* ar.lproj */,
				4C0C3A262B8F1F6800123456 /* de.lproj */,
				4C0C3A272B8F1F6900123456 /* es.lproj */,
				4C0C3A282B8F1F6A00123456 /* fr.lproj */,
				4C0C3A292B8F1F6B00123456 /* hi.lproj */,
				4C0C3A2A2B8F1F6C00123456 /* ja.lproj */,
				4C0C3A2B2B8F1F6D00123456 /* ko.lproj */,
				4C0C3A2C2B8F1F6E00123456 /* pt.lproj */,
				4C0C3A2D2B8F1F6F00123456 /* ru.lproj */,
				4C0C3A2E2B8F1F7000123456 /* zh_TW.lproj */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		94D12CBB2E50E98500B3DDA8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				94D12CBC2E50E98500B3DDA8 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4C0C39FC2B8F1F5B00123456 /* NoSleep */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4C0C3A272B8F1F5E00123456 /* Build configuration list for PBXNativeTarget "NoSleep" */;
			buildPhases = (
				4C0C39F92B8F1F5B00123456 /* Sources */,
				4C0C39FA2B8F1F5B00123456 /* Frameworks */,
				4C0C39FB2B8F1F5B00123456 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NoSleep;
			productName = NoSleep;
			productReference = 4C0C39FD2B8F1F5B00123456 /* NoSleep.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4C0C39F52B8F1F5B00123456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					4C0C39FC2B8F1F5B00123456 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 4C0C39F82B8F1F5B00123456 /* Build configuration list for PBXProject "NoSleep" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 4C0C39F42B8F1F5B00123456;
			productRefGroup = 4C0C39FF2B8F1F5B00123456 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4C0C39FC2B8F1F5B00123456 /* NoSleep */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4C0C39FB2B8F1F5B00123456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4C0C3A082B8F1F5C00123456 /* Preview Content/Preview Content/Preview Assets.xcassets in Resources */,
				4C0C3A052B8F1F5C00123456 /* Assets.xcassets in Resources */,
				4C0C3A2F2B8F1F7100123456 /* en.lproj in Resources */,
				4C0C3A302B8F1F7200123456 /* zh_CN.lproj in Resources */,
				4C0C3A312B8F1F7300123456 /* ar.lproj in Resources */,
				4C0C3A322B8F1F7400123456 /* de.lproj in Resources */,
				4C0C3A332B8F1F7500123456 /* es.lproj in Resources */,
				4C0C3A342B8F1F7600123456 /* fr.lproj in Resources */,
				4C0C3A352B8F1F7700123456 /* hi.lproj in Resources */,
				4C0C3A362B8F1F7800123456 /* ja.lproj in Resources */,
				4C0C3A372B8F1F7900123456 /* ko.lproj in Resources */,
				4C0C3A382B8F1F7A00123456 /* pt.lproj in Resources */,
				4C0C3A392B8F1F7B00123456 /* ru.lproj in Resources */,
				4C0C3A3A2B8F1F7C00123456 /* zh_TW.lproj in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4C0C39F92B8F1F5B00123456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4C0C3A032B8F1F5B00123456 /* ContentView.swift in Sources */,
				4C0C3A162B8F1F5F00123456 /* SettingsView.swift in Sources */,
				4C0C3A182B8F1F6000123456 /* SleepManager.swift in Sources */,
				4C0C3A1A2B8F1F6100123456 /* StatsView.swift in Sources */,
				4C0C3A1C2B8F1F6200123456 /* LanguageSettingsView.swift in Sources */,
				4C0C3A1E2B8F1F6300123456 /* Localization.swift in Sources */,
				4C0C3A3B2B8F1F7D00123456 /* PurchaseManager.swift in Sources */,
				4C0C3A3C2B8F1F7E00123456 /* PaywallView.swift in Sources */,
				4C0C3A3D2B8F1F7F00123456 /* PurchaseView.swift in Sources */,
				4C0C3A012B8F1F5B00123456 /* NoSleepApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		4C0C3A252B8F1F5E00123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = W25945ZTZ4;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4C0C3A262B8F1F5E00123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = W25945ZTZ4;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		4C0C3A282B8F1F5E00123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NoSleep/NoSleep.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NoSleep/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = tech.jiangkang.NoSleep;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		4C0C3A292B8F1F5E00123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NoSleep/NoSleep.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NoSleep/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = tech.jiangkang.NoSleep;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4C0C39F82B8F1F5B00123456 /* Build configuration list for PBXProject "NoSleep" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4C0C3A252B8F1F5E00123456 /* Debug */,
				4C0C3A262B8F1F5E00123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4C0C3A272B8F1F5E00123456 /* Build configuration list for PBXNativeTarget "NoSleep" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4C0C3A282B8F1F5E00123456 /* Debug */,
				4C0C3A292B8F1F5E00123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4C0C39F52B8F1F5B00123456 /* Project object */;
}
