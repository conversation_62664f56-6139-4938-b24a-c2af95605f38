#!/bin/bash

# 编译并运行应用
echo "编译 NoSleep 应用..."
xcodebuild -project NoSleep_Xcode/NoSleep.xcodeproj -scheme NoSleep -configuration Debug build

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "请在以下位置找到应用："
    ls -la "/Users/<USER>/Library/Developer/Xcode/DerivedData/NoSleep-"*/Build/Products/Debug/NoSleep.app
    
    echo ""
    echo "测试步骤："
    echo "1. 启动应用"
    echo "2. 点击窗口左上角的关闭按钮关闭窗口"
    echo "3. 点击菜单栏的月亮图标"
    echo "4. 检查窗口是否正确显示到桌面"
    echo ""
    echo "修复内容："
    echo "- 添加了 window.orderFrontRegardless() 确保窗口显示"
    echo "- 临时提高窗口级别到 .floating 确保窗口置顶"
    echo "- 处理窗口最小化状态"
    echo "- 延迟切换到后台模式，避免过早隐藏"
else
    echo "编译失败！"
    exit 1
fi